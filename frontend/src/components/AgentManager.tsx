import React, { useState, useEffect } from 'react';
import { useApp } from '../context/AppContext';
import { Agent, LLMConfig } from '../types';
import { Plus, Edit, Trash2, User, Brain, Wrench, Settings } from 'lucide-react';
import { getProviderIcon, getProviderColor } from '../utils/llmConfig';
import { storageService } from '../services/storageService';
const AVATAR_OPTIONS = [
  '/images/agent-alex.jpg',
  '/images/agent-luna.jpg',
  '/images/agent-max.jpg',
  '/images/agent-chen.jpg',
  '/images/agent-sam.jpg',
  '/images/agent-robin.jpg',
  '/images/agent-taylor.jpg',
  '/images/agent-zoe.jpg',
];
const EXPERTISE_OPTIONS = [
  '技术', '商业', '设计', '营销', '数据分析', 
  '产品管理', '法律', '心理学', '教育', '医疗'
];
const THINKING_STYLES = [
  { value: 'logical', label: '逻辑型' },
  { value: 'creative', label: '创意型' },
  { value: 'analytical', label: '分析型' },
  { value: 'intuitive', label: '直觉型' },
  { value: 'systematic', label: '系统型' },
];
const PERSONALITIES = [
  { value: 'assertive', label: '果断型' },
  { value: 'collaborative', label: '协作型' },
  { value: 'diplomatic', label: '外交型' },
  { value: 'direct', label: '直接型' },
  { value: 'thoughtful', label: '深思型' },
];
const TOOL_OPTIONS = [
  '数据查询', '市场分析', '技术调研', '用户调研', 
  '竞品分析', '风险评估', '法律咨询', '创意生成'
];
export default function AgentManager() {
  const { state, addAgent, updateAgent, deleteAgent } = useApp();
  const [isAddingAgent, setIsAddingAgent] = useState(false);
  const [editingAgent, setEditingAgent] = useState<Agent | null>(null);

  const handleDeleteAgent = async (agentId: string) => {
    if (confirm('确定要删除这个智能体吗？')) {
      try {
        await deleteAgent(agentId);
      } catch (error) {
        alert('删除智能体失败: ' + (error instanceof Error ? error.message : '未知错误'));
      }
    }
  };

  const handleSubmit = (agentData: Omit<Agent, 'id' | 'isActive'>) => {
    if (editingAgent) {
      updateAgent({ ...agentData, id: editingAgent.id, isActive: editingAgent.isActive });
      setEditingAgent(null);
    } else {
      addAgent(agentData);
      setIsAddingAgent(false);
    }
  };
  return (
    <div className="h-full bg-gradient-to-br from-slate-50 to-blue-50 w-full overflow-y-auto">
      <div className="centered-container">
        <div className="centered-content">
        <div className="flex items-center justify-between mb-8">
          <div>
            <h1 className="text-3xl font-bold text-gray-900 mb-2">智能体管理</h1>
            <p className="text-gray-600">配置和管理您的AI智能体团队</p>
          </div>
          <button
            onClick={() => setIsAddingAgent(true)}
            className="flex items-center gap-2 bg-blue-600 text-white px-6 py-3 rounded-lg hover:bg-blue-700 transition-colors shadow-lg"
          >
            <Plus size={20} />
            添加智能体
          </button>
        </div>
        {/* 智能体列表 */}
        <div className="flex flex-wrap gap-6 mb-8">
          {state.agents.map((agent) => (
            <AgentCard
              key={agent.id}
              agent={agent}
              onEdit={() => setEditingAgent(agent)}
              onDelete={() => handleDeleteAgent(agent.id)}
            />
          ))}
        </div>
        {/* 添加/编辑智能体表单 */}
        {(isAddingAgent || editingAgent) && (
          <AgentForm
            agent={editingAgent}
            onSubmit={handleSubmit}
            onCancel={() => {
              setIsAddingAgent(false);
              setEditingAgent(null);
            }}
          />
        )}
        </div>
      </div>
    </div>
  );
}
function AgentCard({ agent, onEdit, onDelete }: {
  agent: Agent;
  onEdit: () => void;
  onDelete: () => void;
}) {
  return (
    <div className="bg-white rounded-xl shadow-lg hover:shadow-xl transition-shadow p-6 border border-gray-100 fixed-size-card">
      <div className="flex items-center gap-4 mb-4">
        <img
          src={agent.avatar}
          alt={agent.name}
          className="w-16 h-16 rounded-full object-cover border-4 border-blue-100"
        />
        <div>
          <h3 className="text-xl font-semibold text-gray-900">{agent.name}</h3>
          <span className={`inline-block px-2 py-1 rounded-full text-xs font-medium ${
            agent.isActive 
              ? 'bg-green-100 text-green-800' 
              : 'bg-gray-100 text-gray-600'
          }`}>
            {agent.isActive ? '活跃' : '非活跃'}
          </span>
        </div>
      </div>
      <div className="space-y-3 mb-4">
        <div className="flex items-center gap-2">
          <User size={16} className="text-blue-600" />
          <span className="text-sm text-gray-600">专业领域：</span>
          <div className="flex flex-wrap gap-1">
            {agent.expertise.slice(0, 2).map((exp, index) => (
              <span key={index} className="bg-blue-100 text-blue-800 text-xs px-2 py-1 rounded">
                {exp}
              </span>
            ))}
            {agent.expertise.length > 2 && (
              <span className="text-xs text-gray-500">+{agent.expertise.length - 2}</span>
            )}
          </div>
        </div>
        <div className="flex items-center gap-2">
          <Brain size={16} className="text-purple-600" />
          <span className="text-sm text-gray-600">思维方式：</span>
          <span className="text-sm font-medium text-gray-900">
            {THINKING_STYLES.find(s => s.value === agent.thinkingStyle)?.label}
          </span>
        </div>
        <div className="flex items-center gap-2">
          <Wrench size={16} className="text-green-600" />
          <span className="text-sm text-gray-600">工具：</span>
          <span className="text-sm text-gray-500">{agent.tools.length} 个</span>
        </div>
        {agent.llmConfig && (
          <div className="flex items-center gap-2">
            <Settings size={16} className="text-orange-600" />
            <span className="text-sm text-gray-600">LLM：</span>
            <div className="flex items-center gap-1">
              <span className="text-sm">{getProviderIcon(agent.llmConfig.provider)}</span>
              <span className={`text-xs px-2 py-0.5 rounded ${getProviderColor(agent.llmConfig.provider)}`}>
                {agent.llmConfig.name}
              </span>
            </div>
          </div>
        )}
      </div>
      <div className="flex gap-2">
        <button
          onClick={onEdit}
          className="flex-1 bg-blue-50 text-blue-600 py-2 rounded-lg hover:bg-blue-100 transition-colors flex items-center justify-center gap-1"
        >
          <Edit size={16} />
          编辑
        </button>
        <button
          onClick={onDelete}
          className="flex-1 bg-red-50 text-red-600 py-2 rounded-lg hover:bg-red-100 transition-colors flex items-center justify-center gap-1"
        >
          <Trash2 size={16} />
          删除
        </button>
      </div>
    </div>
  );
}
function AgentForm({ agent, onSubmit, onCancel }: {
  agent: Agent | null;
  onSubmit: (data: Omit<Agent, 'id' | 'isActive'>) => void;
  onCancel: () => void;
}) {
  const [formData, setFormData] = useState({
    name: agent?.name || '',
    avatar: agent?.avatar || AVATAR_OPTIONS[0],
    expertise: agent?.expertise || [],
    thinkingStyle: agent?.thinkingStyle || 'logical',
    personality: agent?.personality || 'collaborative',
    tools: agent?.tools || [],
    llmConfig: agent?.llmConfig || null,
  });
  const [availableLLMConfigs, setAvailableLLMConfigs] = useState<LLMConfig[]>([]);
  const [loadingConfigs, setLoadingConfigs] = useState(true);

  useEffect(() => {
    // 加载可用的LLM配置
    const loadConfigs = async () => {
      try {
        setLoadingConfigs(true);
        const configs = await storageService.getLLMConfigs();
        setAvailableLLMConfigs(configs);
      } catch (error) {
        console.error('Failed to load LLM configs:', error);
        setAvailableLLMConfigs([]);
      } finally {
        setLoadingConfigs(false);
      }
    };

    loadConfigs();
  }, []);
  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (formData.name && formData.expertise.length > 0 && formData.llmConfig) {
      onSubmit({
        ...formData,
        llmConfig: formData.llmConfig
      });
    }
  };
  const toggleExpertise = (exp: string) => {
    setFormData(prev => ({
      ...prev,
      expertise: prev.expertise.includes(exp)
        ? prev.expertise.filter(e => e !== exp)
        : [...prev.expertise, exp]
    }));
  };
  const toggleTool = (tool: string) => {
    setFormData(prev => ({
      ...prev,
      tools: prev.tools.includes(tool)
        ? prev.tools.filter(t => t !== tool)
        : [...prev.tools, tool]
    }));
  };
  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
      <div className="bg-white rounded-xl shadow-2xl max-w-2xl w-full max-h-[90vh] overflow-y-auto">
        <div className="p-6 border-b border-gray-200">
          <h2 className="text-2xl font-bold text-gray-900">
            {agent ? '编辑智能体' : '添加新智能体'}
          </h2>
        </div>
        <form onSubmit={handleSubmit} className="p-6 space-y-6">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              智能体名称
            </label>
            <input
              type="text"
              value={formData.name}
              onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              placeholder="输入智能体名称"
              required
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              选择头像
            </label>
            <div className="grid grid-cols-4 gap-3">
              {AVATAR_OPTIONS.map((avatar, index) => (
                <button
                  key={index}
                  type="button"
                  onClick={() => setFormData(prev => ({ ...prev, avatar }))}
                  className={`relative rounded-lg overflow-hidden border-4 transition-all ${
                    formData.avatar === avatar 
                      ? 'border-blue-500 ring-2 ring-blue-200' 
                      : 'border-gray-200 hover:border-gray-300'
                  }`}
                >
                  <img src={avatar} alt={`Avatar ${index + 1}`} className="w-16 h-16 object-cover" />
                </button>
              ))}
            </div>
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              专业领域（至少选择一个）
            </label>
            <div className="grid grid-cols-2 gap-2">
              {EXPERTISE_OPTIONS.map((exp) => (
                <button
                  key={exp}
                  type="button"
                  onClick={() => toggleExpertise(exp)}
                  className={`px-3 py-2 rounded-lg text-sm transition-colors ${
                    formData.expertise.includes(exp)
                      ? 'bg-blue-100 text-blue-800 border-2 border-blue-300'
                      : 'bg-gray-100 text-gray-700 border-2 border-gray-300 hover:bg-gray-200'
                  }`}
                >
                  {exp}
                </button>
              ))}
            </div>
          </div>
          <div className="grid grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                思维方式
              </label>
              <select
                value={formData.thinkingStyle}
                onChange={(e) => setFormData(prev => ({ ...prev, thinkingStyle: e.target.value }))}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              >
                {THINKING_STYLES.map((style) => (
                  <option key={style.value} value={style.value}>
                    {style.label}
                  </option>
                ))}
              </select>
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                性格特征
              </label>
              <select
                value={formData.personality}
                onChange={(e) => setFormData(prev => ({ ...prev, personality: e.target.value }))}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              >
                {PERSONALITIES.map((personality) => (
                  <option key={personality.value} value={personality.value}>
                    {personality.label}
                  </option>
                ))}
              </select>
            </div>
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              可用工具
            </label>
            <div className="grid grid-cols-2 gap-2">
              {TOOL_OPTIONS.map((tool) => (
                <button
                  key={tool}
                  type="button"
                  onClick={() => toggleTool(tool)}
                  className={`px-3 py-2 rounded-lg text-sm transition-colors ${
                    formData.tools.includes(tool)
                      ? 'bg-green-100 text-green-800 border-2 border-green-300'
                      : 'bg-gray-100 text-gray-700 border-2 border-gray-300 hover:bg-gray-200'
                  }`}
                >
                  {tool}
                </button>
              ))}
            </div>
          </div>

          {/* LLM配置选择 */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              LLM配置 <span className="text-red-500">*</span>
            </label>
            {loadingConfigs ? (
              <div className="w-full px-3 py-2 border border-gray-300 rounded-lg bg-gray-50 flex items-center gap-2">
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600"></div>
                <span className="text-gray-600">正在加载LLM配置...</span>
              </div>
            ) : (
              <select
                value={formData.llmConfig?.id || ''}
                onChange={(e) => {
                  const selectedConfig = availableLLMConfigs.find(config => config.id === e.target.value);
                  setFormData(prev => ({ ...prev, llmConfig: selectedConfig || null }));
                }}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                required
              >
                <option value="">请选择LLM配置</option>
                {availableLLMConfigs.map((config) => (
                  <option key={config.id} value={config.id}>
                    {getProviderIcon(config.provider)} {config.name} ({config.provider.toUpperCase()})
                  </option>
                ))}
              </select>
            )}
            {formData.llmConfig && (
              <div className="mt-2 p-3 bg-gray-50 rounded-lg">
                <div className="flex items-center gap-2 mb-1">
                  <span className="text-sm font-medium text-gray-700">已选择:</span>
                  <span className={`text-xs px-2 py-1 rounded ${getProviderColor(formData.llmConfig.provider)}`}>
                    {formData.llmConfig.name}
                  </span>
                </div>
                <div className="text-xs text-gray-600">
                  模型: {formData.llmConfig.model} | 温度: {formData.llmConfig.temperature} | 令牌: {formData.llmConfig.maxTokens}
                </div>
              </div>
            )}
            {availableLLMConfigs.length === 0 && (
              <div className="mt-2 p-3 bg-yellow-50 border border-yellow-200 rounded-lg">
                <p className="text-sm text-yellow-800">
                  暂无可用的LLM配置。请先前往
                  <span className="font-medium">LLM配置</span>
                  页面创建LLM配置，然后再创建智能体。
                </p>
              </div>
            )}
          </div>
          <div className="flex gap-3 pt-4 border-t border-gray-200">
            <button
              type="button"
              onClick={onCancel}
              className="flex-1 px-4 py-3 bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200 transition-colors"
            >
              取消
            </button>
            <button
              type="submit"
              disabled={loadingConfigs || availableLLMConfigs.length === 0}
              className="flex-1 px-4 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:bg-gray-400 disabled:cursor-not-allowed transition-colors"
            >
              {loadingConfigs ? '加载中...' : `${agent ? '更新' : '创建'}智能体`}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
}
// const handleDeleteAgent = async (agentId: string) => {
//   if (confirm('确定要删除这个智能体吗？')) {
//     try {
//       await deleteAgent(agentId);
//     } catch (error) {
//       alert('删除智能体失败: ' + (error instanceof Error ? error.message : '未知错误'));
//     }
//   }
// };
