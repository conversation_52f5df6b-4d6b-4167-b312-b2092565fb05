var Wc=Object.defineProperty;var Qc=(e,t,n)=>t in e?Wc(e,t,{enumerable:!0,configurable:!0,writable:!0,value:n}):e[t]=n;var Ce=(e,t,n)=>(Qc(e,typeof t!="symbol"?t+"":t,n),n);(function(){const t=document.createElement("link").relList;if(t&&t.supports&&t.supports("modulepreload"))return;for(const s of document.querySelectorAll('link[rel="modulepreload"]'))r(s);new MutationObserver(s=>{for(const i of s)if(i.type==="childList")for(const o of i.addedNodes)o.tagName==="LINK"&&o.rel==="modulepreload"&&r(o)}).observe(document,{childList:!0,subtree:!0});function n(s){const i={};return s.integrity&&(i.integrity=s.integrity),s.referrerPolicy&&(i.referrerPolicy=s.referrerPolicy),s.crossOrigin==="use-credentials"?i.credentials="include":s.crossOrigin==="anonymous"?i.credentials="omit":i.credentials="same-origin",i}function r(s){if(s.ep)return;s.ep=!0;const i=n(s);fetch(s.href,i)}})();function Kc(e){return e&&e.__esModule&&Object.prototype.hasOwnProperty.call(e,"default")?e.default:e}var Sa={exports:{}},vs={},ka={exports:{}},_={};/**
 * @license React
 * react.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var ur=Symbol.for("react.element"),Gc=Symbol.for("react.portal"),Yc=Symbol.for("react.fragment"),Xc=Symbol.for("react.strict_mode"),Zc=Symbol.for("react.profiler"),Jc=Symbol.for("react.provider"),qc=Symbol.for("react.context"),ed=Symbol.for("react.forward_ref"),td=Symbol.for("react.suspense"),nd=Symbol.for("react.memo"),rd=Symbol.for("react.lazy"),oo=Symbol.iterator;function sd(e){return e===null||typeof e!="object"?null:(e=oo&&e[oo]||e["@@iterator"],typeof e=="function"?e:null)}var Ca={isMounted:function(){return!1},enqueueForceUpdate:function(){},enqueueReplaceState:function(){},enqueueSetState:function(){}},Ea=Object.assign,La={};function yn(e,t,n){this.props=e,this.context=t,this.refs=La,this.updater=n||Ca}yn.prototype.isReactComponent={};yn.prototype.setState=function(e,t){if(typeof e!="object"&&typeof e!="function"&&e!=null)throw Error("setState(...): takes an object of state variables to update or a function which returns an object of state variables.");this.updater.enqueueSetState(this,e,t,"setState")};yn.prototype.forceUpdate=function(e){this.updater.enqueueForceUpdate(this,e,"forceUpdate")};function ba(){}ba.prototype=yn.prototype;function di(e,t,n){this.props=e,this.context=t,this.refs=La,this.updater=n||Ca}var fi=di.prototype=new ba;fi.constructor=di;Ea(fi,yn.prototype);fi.isPureReactComponent=!0;var ao=Array.isArray,Ma=Object.prototype.hasOwnProperty,pi={current:null},Ta={key:!0,ref:!0,__self:!0,__source:!0};function Da(e,t,n){var r,s={},i=null,o=null;if(t!=null)for(r in t.ref!==void 0&&(o=t.ref),t.key!==void 0&&(i=""+t.key),t)Ma.call(t,r)&&!Ta.hasOwnProperty(r)&&(s[r]=t[r]);var a=arguments.length-2;if(a===1)s.children=n;else if(1<a){for(var u=Array(a),p=0;p<a;p++)u[p]=arguments[p+2];s.children=u}if(e&&e.defaultProps)for(r in a=e.defaultProps,a)s[r]===void 0&&(s[r]=a[r]);return{$$typeof:ur,type:e,key:i,ref:o,props:s,_owner:pi.current}}function ld(e,t){return{$$typeof:ur,type:e.type,key:t,ref:e.ref,props:e.props,_owner:e._owner}}function mi(e){return typeof e=="object"&&e!==null&&e.$$typeof===ur}function id(e){var t={"=":"=0",":":"=2"};return"$"+e.replace(/[=:]/g,function(n){return t[n]})}var uo=/\/+/g;function Os(e,t){return typeof e=="object"&&e!==null&&e.key!=null?id(""+e.key):t.toString(36)}function _r(e,t,n,r,s){var i=typeof e;(i==="undefined"||i==="boolean")&&(e=null);var o=!1;if(e===null)o=!0;else switch(i){case"string":case"number":o=!0;break;case"object":switch(e.$$typeof){case ur:case Gc:o=!0}}if(o)return o=e,s=s(o),e=r===""?"."+Os(o,0):r,ao(s)?(n="",e!=null&&(n=e.replace(uo,"$&/")+"/"),_r(s,t,n,"",function(p){return p})):s!=null&&(mi(s)&&(s=ld(s,n+(!s.key||o&&o.key===s.key?"":(""+s.key).replace(uo,"$&/")+"/")+e)),t.push(s)),1;if(o=0,r=r===""?".":r+":",ao(e))for(var a=0;a<e.length;a++){i=e[a];var u=r+Os(i,a);o+=_r(i,t,n,u,s)}else if(u=sd(e),typeof u=="function")for(e=u.call(e),a=0;!(i=e.next()).done;)i=i.value,u=r+Os(i,a++),o+=_r(i,t,n,u,s);else if(i==="object")throw t=String(e),Error("Objects are not valid as a React child (found: "+(t==="[object Object]"?"object with keys {"+Object.keys(e).join(", ")+"}":t)+"). If you meant to render a collection of children, use an array instead.");return o}function gr(e,t,n){if(e==null)return e;var r=[],s=0;return _r(e,r,"","",function(i){return t.call(n,i,s++)}),r}function od(e){if(e._status===-1){var t=e._result;t=t(),t.then(function(n){(e._status===0||e._status===-1)&&(e._status=1,e._result=n)},function(n){(e._status===0||e._status===-1)&&(e._status=2,e._result=n)}),e._status===-1&&(e._status=0,e._result=t)}if(e._status===1)return e._result.default;throw e._result}var fe={current:null},zr={transition:null},ad={ReactCurrentDispatcher:fe,ReactCurrentBatchConfig:zr,ReactCurrentOwner:pi};function Aa(){throw Error("act(...) is not supported in production builds of React.")}_.Children={map:gr,forEach:function(e,t,n){gr(e,function(){t.apply(this,arguments)},n)},count:function(e){var t=0;return gr(e,function(){t++}),t},toArray:function(e){return gr(e,function(t){return t})||[]},only:function(e){if(!mi(e))throw Error("React.Children.only expected to receive a single React element child.");return e}};_.Component=yn;_.Fragment=Yc;_.Profiler=Zc;_.PureComponent=di;_.StrictMode=Xc;_.Suspense=td;_.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED=ad;_.act=Aa;_.cloneElement=function(e,t,n){if(e==null)throw Error("React.cloneElement(...): The argument must be a React element, but you passed "+e+".");var r=Ea({},e.props),s=e.key,i=e.ref,o=e._owner;if(t!=null){if(t.ref!==void 0&&(i=t.ref,o=pi.current),t.key!==void 0&&(s=""+t.key),e.type&&e.type.defaultProps)var a=e.type.defaultProps;for(u in t)Ma.call(t,u)&&!Ta.hasOwnProperty(u)&&(r[u]=t[u]===void 0&&a!==void 0?a[u]:t[u])}var u=arguments.length-2;if(u===1)r.children=n;else if(1<u){a=Array(u);for(var p=0;p<u;p++)a[p]=arguments[p+2];r.children=a}return{$$typeof:ur,type:e.type,key:s,ref:i,props:r,_owner:o}};_.createContext=function(e){return e={$$typeof:qc,_currentValue:e,_currentValue2:e,_threadCount:0,Provider:null,Consumer:null,_defaultValue:null,_globalName:null},e.Provider={$$typeof:Jc,_context:e},e.Consumer=e};_.createElement=Da;_.createFactory=function(e){var t=Da.bind(null,e);return t.type=e,t};_.createRef=function(){return{current:null}};_.forwardRef=function(e){return{$$typeof:ed,render:e}};_.isValidElement=mi;_.lazy=function(e){return{$$typeof:rd,_payload:{_status:-1,_result:e},_init:od}};_.memo=function(e,t){return{$$typeof:nd,type:e,compare:t===void 0?null:t}};_.startTransition=function(e){var t=zr.transition;zr.transition={};try{e()}finally{zr.transition=t}};_.unstable_act=Aa;_.useCallback=function(e,t){return fe.current.useCallback(e,t)};_.useContext=function(e){return fe.current.useContext(e)};_.useDebugValue=function(){};_.useDeferredValue=function(e){return fe.current.useDeferredValue(e)};_.useEffect=function(e,t){return fe.current.useEffect(e,t)};_.useId=function(){return fe.current.useId()};_.useImperativeHandle=function(e,t,n){return fe.current.useImperativeHandle(e,t,n)};_.useInsertionEffect=function(e,t){return fe.current.useInsertionEffect(e,t)};_.useLayoutEffect=function(e,t){return fe.current.useLayoutEffect(e,t)};_.useMemo=function(e,t){return fe.current.useMemo(e,t)};_.useReducer=function(e,t,n){return fe.current.useReducer(e,t,n)};_.useRef=function(e){return fe.current.useRef(e)};_.useState=function(e){return fe.current.useState(e)};_.useSyncExternalStore=function(e,t,n){return fe.current.useSyncExternalStore(e,t,n)};_.useTransition=function(){return fe.current.useTransition()};_.version="18.3.1";ka.exports=_;var M=ka.exports;const Pa=Kc(M);/**
 * @license React
 * react-jsx-runtime.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var ud=M,cd=Symbol.for("react.element"),dd=Symbol.for("react.fragment"),fd=Object.prototype.hasOwnProperty,pd=ud.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.ReactCurrentOwner,md={key:!0,ref:!0,__self:!0,__source:!0};function _a(e,t,n){var r,s={},i=null,o=null;n!==void 0&&(i=""+n),t.key!==void 0&&(i=""+t.key),t.ref!==void 0&&(o=t.ref);for(r in t)fd.call(t,r)&&!md.hasOwnProperty(r)&&(s[r]=t[r]);if(e&&e.defaultProps)for(r in t=e.defaultProps,t)s[r]===void 0&&(s[r]=t[r]);return{$$typeof:cd,type:e,key:i,ref:o,props:s,_owner:pd.current}}vs.Fragment=dd;vs.jsx=_a;vs.jsxs=_a;Sa.exports=vs;var l=Sa.exports,dl={},za={exports:{}},Se={},Ia={exports:{}},Ra={};/**
 * @license React
 * scheduler.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */(function(e){function t(E,T){var P=E.length;E.push(T);e:for(;0<P;){var G=P-1>>>1,q=E[G];if(0<s(q,T))E[G]=T,E[P]=q,P=G;else break e}}function n(E){return E.length===0?null:E[0]}function r(E){if(E.length===0)return null;var T=E[0],P=E.pop();if(P!==T){E[0]=P;e:for(var G=0,q=E.length,mr=q>>>1;G<mr;){var kt=2*(G+1)-1,Rs=E[kt],Ct=kt+1,hr=E[Ct];if(0>s(Rs,P))Ct<q&&0>s(hr,Rs)?(E[G]=hr,E[Ct]=P,G=Ct):(E[G]=Rs,E[kt]=P,G=kt);else if(Ct<q&&0>s(hr,P))E[G]=hr,E[Ct]=P,G=Ct;else break e}}return T}function s(E,T){var P=E.sortIndex-T.sortIndex;return P!==0?P:E.id-T.id}if(typeof performance=="object"&&typeof performance.now=="function"){var i=performance;e.unstable_now=function(){return i.now()}}else{var o=Date,a=o.now();e.unstable_now=function(){return o.now()-a}}var u=[],p=[],h=1,m=null,x=3,y=!1,w=!1,v=!1,N=typeof setTimeout=="function"?setTimeout:null,d=typeof clearTimeout=="function"?clearTimeout:null,c=typeof setImmediate<"u"?setImmediate:null;typeof navigator<"u"&&navigator.scheduling!==void 0&&navigator.scheduling.isInputPending!==void 0&&navigator.scheduling.isInputPending.bind(navigator.scheduling);function f(E){for(var T=n(p);T!==null;){if(T.callback===null)r(p);else if(T.startTime<=E)r(p),T.sortIndex=T.expirationTime,t(u,T);else break;T=n(p)}}function g(E){if(v=!1,f(E),!w)if(n(u)!==null)w=!0,zs(j);else{var T=n(p);T!==null&&Is(g,T.startTime-E)}}function j(E,T){w=!1,v&&(v=!1,d(b),b=-1),y=!0;var P=x;try{for(f(T),m=n(u);m!==null&&(!(m.expirationTime>T)||E&&!Ae());){var G=m.callback;if(typeof G=="function"){m.callback=null,x=m.priorityLevel;var q=G(m.expirationTime<=T);T=e.unstable_now(),typeof q=="function"?m.callback=q:m===n(u)&&r(u),f(T)}else r(u);m=n(u)}if(m!==null)var mr=!0;else{var kt=n(p);kt!==null&&Is(g,kt.startTime-T),mr=!1}return mr}finally{m=null,x=P,y=!1}}var k=!1,L=null,b=-1,$=5,A=-1;function Ae(){return!(e.unstable_now()-A<$)}function Nn(){if(L!==null){var E=e.unstable_now();A=E;var T=!0;try{T=L(!0,E)}finally{T?Sn():(k=!1,L=null)}}else k=!1}var Sn;if(typeof c=="function")Sn=function(){c(Nn)};else if(typeof MessageChannel<"u"){var io=new MessageChannel,Bc=io.port2;io.port1.onmessage=Nn,Sn=function(){Bc.postMessage(null)}}else Sn=function(){N(Nn,0)};function zs(E){L=E,k||(k=!0,Sn())}function Is(E,T){b=N(function(){E(e.unstable_now())},T)}e.unstable_IdlePriority=5,e.unstable_ImmediatePriority=1,e.unstable_LowPriority=4,e.unstable_NormalPriority=3,e.unstable_Profiling=null,e.unstable_UserBlockingPriority=2,e.unstable_cancelCallback=function(E){E.callback=null},e.unstable_continueExecution=function(){w||y||(w=!0,zs(j))},e.unstable_forceFrameRate=function(E){0>E||125<E?console.error("forceFrameRate takes a positive int between 0 and 125, forcing frame rates higher than 125 fps is not supported"):$=0<E?Math.floor(1e3/E):5},e.unstable_getCurrentPriorityLevel=function(){return x},e.unstable_getFirstCallbackNode=function(){return n(u)},e.unstable_next=function(E){switch(x){case 1:case 2:case 3:var T=3;break;default:T=x}var P=x;x=T;try{return E()}finally{x=P}},e.unstable_pauseExecution=function(){},e.unstable_requestPaint=function(){},e.unstable_runWithPriority=function(E,T){switch(E){case 1:case 2:case 3:case 4:case 5:break;default:E=3}var P=x;x=E;try{return T()}finally{x=P}},e.unstable_scheduleCallback=function(E,T,P){var G=e.unstable_now();switch(typeof P=="object"&&P!==null?(P=P.delay,P=typeof P=="number"&&0<P?G+P:G):P=G,E){case 1:var q=-1;break;case 2:q=250;break;case 5:q=**********;break;case 4:q=1e4;break;default:q=5e3}return q=P+q,E={id:h++,callback:T,priorityLevel:E,startTime:P,expirationTime:q,sortIndex:-1},P>G?(E.sortIndex=P,t(p,E),n(u)===null&&E===n(p)&&(v?(d(b),b=-1):v=!0,Is(g,P-G))):(E.sortIndex=q,t(u,E),w||y||(w=!0,zs(j))),E},e.unstable_shouldYield=Ae,e.unstable_wrapCallback=function(E){var T=x;return function(){var P=x;x=T;try{return E.apply(this,arguments)}finally{x=P}}}})(Ra);Ia.exports=Ra;var hd=Ia.exports;/**
 * @license React
 * react-dom.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var gd=M,Ne=hd;function S(e){for(var t="https://reactjs.org/docs/error-decoder.html?invariant="+e,n=1;n<arguments.length;n++)t+="&args[]="+encodeURIComponent(arguments[n]);return"Minified React error #"+e+"; visit "+t+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}var Oa=new Set,Wn={};function Ut(e,t){an(e,t),an(e+"Capture",t)}function an(e,t){for(Wn[e]=t,e=0;e<t.length;e++)Oa.add(t[e])}var Ze=!(typeof window>"u"||typeof window.document>"u"||typeof window.document.createElement>"u"),fl=Object.prototype.hasOwnProperty,xd=/^[:A-Z_a-z\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD][:A-Z_a-z\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD\-.0-9\u00B7\u0300-\u036F\u203F-\u2040]*$/,co={},fo={};function yd(e){return fl.call(fo,e)?!0:fl.call(co,e)?!1:xd.test(e)?fo[e]=!0:(co[e]=!0,!1)}function vd(e,t,n,r){if(n!==null&&n.type===0)return!1;switch(typeof t){case"function":case"symbol":return!0;case"boolean":return r?!1:n!==null?!n.acceptsBooleans:(e=e.toLowerCase().slice(0,5),e!=="data-"&&e!=="aria-");default:return!1}}function wd(e,t,n,r){if(t===null||typeof t>"u"||vd(e,t,n,r))return!0;if(r)return!1;if(n!==null)switch(n.type){case 3:return!t;case 4:return t===!1;case 5:return isNaN(t);case 6:return isNaN(t)||1>t}return!1}function pe(e,t,n,r,s,i,o){this.acceptsBooleans=t===2||t===3||t===4,this.attributeName=r,this.attributeNamespace=s,this.mustUseProperty=n,this.propertyName=e,this.type=t,this.sanitizeURL=i,this.removeEmptyString=o}var le={};"children dangerouslySetInnerHTML defaultValue defaultChecked innerHTML suppressContentEditableWarning suppressHydrationWarning style".split(" ").forEach(function(e){le[e]=new pe(e,0,!1,e,null,!1,!1)});[["acceptCharset","accept-charset"],["className","class"],["htmlFor","for"],["httpEquiv","http-equiv"]].forEach(function(e){var t=e[0];le[t]=new pe(t,1,!1,e[1],null,!1,!1)});["contentEditable","draggable","spellCheck","value"].forEach(function(e){le[e]=new pe(e,2,!1,e.toLowerCase(),null,!1,!1)});["autoReverse","externalResourcesRequired","focusable","preserveAlpha"].forEach(function(e){le[e]=new pe(e,2,!1,e,null,!1,!1)});"allowFullScreen async autoFocus autoPlay controls default defer disabled disablePictureInPicture disableRemotePlayback formNoValidate hidden loop noModule noValidate open playsInline readOnly required reversed scoped seamless itemScope".split(" ").forEach(function(e){le[e]=new pe(e,3,!1,e.toLowerCase(),null,!1,!1)});["checked","multiple","muted","selected"].forEach(function(e){le[e]=new pe(e,3,!0,e,null,!1,!1)});["capture","download"].forEach(function(e){le[e]=new pe(e,4,!1,e,null,!1,!1)});["cols","rows","size","span"].forEach(function(e){le[e]=new pe(e,6,!1,e,null,!1,!1)});["rowSpan","start"].forEach(function(e){le[e]=new pe(e,5,!1,e.toLowerCase(),null,!1,!1)});var hi=/[\-:]([a-z])/g;function gi(e){return e[1].toUpperCase()}"accent-height alignment-baseline arabic-form baseline-shift cap-height clip-path clip-rule color-interpolation color-interpolation-filters color-profile color-rendering dominant-baseline enable-background fill-opacity fill-rule flood-color flood-opacity font-family font-size font-size-adjust font-stretch font-style font-variant font-weight glyph-name glyph-orientation-horizontal glyph-orientation-vertical horiz-adv-x horiz-origin-x image-rendering letter-spacing lighting-color marker-end marker-mid marker-start overline-position overline-thickness paint-order panose-1 pointer-events rendering-intent shape-rendering stop-color stop-opacity strikethrough-position strikethrough-thickness stroke-dasharray stroke-dashoffset stroke-linecap stroke-linejoin stroke-miterlimit stroke-opacity stroke-width text-anchor text-decoration text-rendering underline-position underline-thickness unicode-bidi unicode-range units-per-em v-alphabetic v-hanging v-ideographic v-mathematical vector-effect vert-adv-y vert-origin-x vert-origin-y word-spacing writing-mode xmlns:xlink x-height".split(" ").forEach(function(e){var t=e.replace(hi,gi);le[t]=new pe(t,1,!1,e,null,!1,!1)});"xlink:actuate xlink:arcrole xlink:role xlink:show xlink:title xlink:type".split(" ").forEach(function(e){var t=e.replace(hi,gi);le[t]=new pe(t,1,!1,e,"http://www.w3.org/1999/xlink",!1,!1)});["xml:base","xml:lang","xml:space"].forEach(function(e){var t=e.replace(hi,gi);le[t]=new pe(t,1,!1,e,"http://www.w3.org/XML/1998/namespace",!1,!1)});["tabIndex","crossOrigin"].forEach(function(e){le[e]=new pe(e,1,!1,e.toLowerCase(),null,!1,!1)});le.xlinkHref=new pe("xlinkHref",1,!1,"xlink:href","http://www.w3.org/1999/xlink",!0,!1);["src","href","action","formAction"].forEach(function(e){le[e]=new pe(e,1,!1,e.toLowerCase(),null,!0,!0)});function xi(e,t,n,r){var s=le.hasOwnProperty(t)?le[t]:null;(s!==null?s.type!==0:r||!(2<t.length)||t[0]!=="o"&&t[0]!=="O"||t[1]!=="n"&&t[1]!=="N")&&(wd(t,n,s,r)&&(n=null),r||s===null?yd(t)&&(n===null?e.removeAttribute(t):e.setAttribute(t,""+n)):s.mustUseProperty?e[s.propertyName]=n===null?s.type===3?!1:"":n:(t=s.attributeName,r=s.attributeNamespace,n===null?e.removeAttribute(t):(s=s.type,n=s===3||s===4&&n===!0?"":""+n,r?e.setAttributeNS(r,t,n):e.setAttribute(t,n))))}var tt=gd.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED,xr=Symbol.for("react.element"),Ht=Symbol.for("react.portal"),Bt=Symbol.for("react.fragment"),yi=Symbol.for("react.strict_mode"),pl=Symbol.for("react.profiler"),$a=Symbol.for("react.provider"),Ua=Symbol.for("react.context"),vi=Symbol.for("react.forward_ref"),ml=Symbol.for("react.suspense"),hl=Symbol.for("react.suspense_list"),wi=Symbol.for("react.memo"),rt=Symbol.for("react.lazy"),Fa=Symbol.for("react.offscreen"),po=Symbol.iterator;function kn(e){return e===null||typeof e!="object"?null:(e=po&&e[po]||e["@@iterator"],typeof e=="function"?e:null)}var Q=Object.assign,$s;function An(e){if($s===void 0)try{throw Error()}catch(n){var t=n.stack.trim().match(/\n( *(at )?)/);$s=t&&t[1]||""}return`
`+$s+e}var Us=!1;function Fs(e,t){if(!e||Us)return"";Us=!0;var n=Error.prepareStackTrace;Error.prepareStackTrace=void 0;try{if(t)if(t=function(){throw Error()},Object.defineProperty(t.prototype,"props",{set:function(){throw Error()}}),typeof Reflect=="object"&&Reflect.construct){try{Reflect.construct(t,[])}catch(p){var r=p}Reflect.construct(e,[],t)}else{try{t.call()}catch(p){r=p}e.call(t.prototype)}else{try{throw Error()}catch(p){r=p}e()}}catch(p){if(p&&r&&typeof p.stack=="string"){for(var s=p.stack.split(`
`),i=r.stack.split(`
`),o=s.length-1,a=i.length-1;1<=o&&0<=a&&s[o]!==i[a];)a--;for(;1<=o&&0<=a;o--,a--)if(s[o]!==i[a]){if(o!==1||a!==1)do if(o--,a--,0>a||s[o]!==i[a]){var u=`
`+s[o].replace(" at new "," at ");return e.displayName&&u.includes("<anonymous>")&&(u=u.replace("<anonymous>",e.displayName)),u}while(1<=o&&0<=a);break}}}finally{Us=!1,Error.prepareStackTrace=n}return(e=e?e.displayName||e.name:"")?An(e):""}function jd(e){switch(e.tag){case 5:return An(e.type);case 16:return An("Lazy");case 13:return An("Suspense");case 19:return An("SuspenseList");case 0:case 2:case 15:return e=Fs(e.type,!1),e;case 11:return e=Fs(e.type.render,!1),e;case 1:return e=Fs(e.type,!0),e;default:return""}}function gl(e){if(e==null)return null;if(typeof e=="function")return e.displayName||e.name||null;if(typeof e=="string")return e;switch(e){case Bt:return"Fragment";case Ht:return"Portal";case pl:return"Profiler";case yi:return"StrictMode";case ml:return"Suspense";case hl:return"SuspenseList"}if(typeof e=="object")switch(e.$$typeof){case Ua:return(e.displayName||"Context")+".Consumer";case $a:return(e._context.displayName||"Context")+".Provider";case vi:var t=e.render;return e=e.displayName,e||(e=t.displayName||t.name||"",e=e!==""?"ForwardRef("+e+")":"ForwardRef"),e;case wi:return t=e.displayName||null,t!==null?t:gl(e.type)||"Memo";case rt:t=e._payload,e=e._init;try{return gl(e(t))}catch{}}return null}function Nd(e){var t=e.type;switch(e.tag){case 24:return"Cache";case 9:return(t.displayName||"Context")+".Consumer";case 10:return(t._context.displayName||"Context")+".Provider";case 18:return"DehydratedFragment";case 11:return e=t.render,e=e.displayName||e.name||"",t.displayName||(e!==""?"ForwardRef("+e+")":"ForwardRef");case 7:return"Fragment";case 5:return t;case 4:return"Portal";case 3:return"Root";case 6:return"Text";case 16:return gl(t);case 8:return t===yi?"StrictMode":"Mode";case 22:return"Offscreen";case 12:return"Profiler";case 21:return"Scope";case 13:return"Suspense";case 19:return"SuspenseList";case 25:return"TracingMarker";case 1:case 0:case 17:case 2:case 14:case 15:if(typeof t=="function")return t.displayName||t.name||null;if(typeof t=="string")return t}return null}function yt(e){switch(typeof e){case"boolean":case"number":case"string":case"undefined":return e;case"object":return e;default:return""}}function Va(e){var t=e.type;return(e=e.nodeName)&&e.toLowerCase()==="input"&&(t==="checkbox"||t==="radio")}function Sd(e){var t=Va(e)?"checked":"value",n=Object.getOwnPropertyDescriptor(e.constructor.prototype,t),r=""+e[t];if(!e.hasOwnProperty(t)&&typeof n<"u"&&typeof n.get=="function"&&typeof n.set=="function"){var s=n.get,i=n.set;return Object.defineProperty(e,t,{configurable:!0,get:function(){return s.call(this)},set:function(o){r=""+o,i.call(this,o)}}),Object.defineProperty(e,t,{enumerable:n.enumerable}),{getValue:function(){return r},setValue:function(o){r=""+o},stopTracking:function(){e._valueTracker=null,delete e[t]}}}}function yr(e){e._valueTracker||(e._valueTracker=Sd(e))}function Ha(e){if(!e)return!1;var t=e._valueTracker;if(!t)return!0;var n=t.getValue(),r="";return e&&(r=Va(e)?e.checked?"true":"false":e.value),e=r,e!==n?(t.setValue(e),!0):!1}function Qr(e){if(e=e||(typeof document<"u"?document:void 0),typeof e>"u")return null;try{return e.activeElement||e.body}catch{return e.body}}function xl(e,t){var n=t.checked;return Q({},t,{defaultChecked:void 0,defaultValue:void 0,value:void 0,checked:n??e._wrapperState.initialChecked})}function mo(e,t){var n=t.defaultValue==null?"":t.defaultValue,r=t.checked!=null?t.checked:t.defaultChecked;n=yt(t.value!=null?t.value:n),e._wrapperState={initialChecked:r,initialValue:n,controlled:t.type==="checkbox"||t.type==="radio"?t.checked!=null:t.value!=null}}function Ba(e,t){t=t.checked,t!=null&&xi(e,"checked",t,!1)}function yl(e,t){Ba(e,t);var n=yt(t.value),r=t.type;if(n!=null)r==="number"?(n===0&&e.value===""||e.value!=n)&&(e.value=""+n):e.value!==""+n&&(e.value=""+n);else if(r==="submit"||r==="reset"){e.removeAttribute("value");return}t.hasOwnProperty("value")?vl(e,t.type,n):t.hasOwnProperty("defaultValue")&&vl(e,t.type,yt(t.defaultValue)),t.checked==null&&t.defaultChecked!=null&&(e.defaultChecked=!!t.defaultChecked)}function ho(e,t,n){if(t.hasOwnProperty("value")||t.hasOwnProperty("defaultValue")){var r=t.type;if(!(r!=="submit"&&r!=="reset"||t.value!==void 0&&t.value!==null))return;t=""+e._wrapperState.initialValue,n||t===e.value||(e.value=t),e.defaultValue=t}n=e.name,n!==""&&(e.name=""),e.defaultChecked=!!e._wrapperState.initialChecked,n!==""&&(e.name=n)}function vl(e,t,n){(t!=="number"||Qr(e.ownerDocument)!==e)&&(n==null?e.defaultValue=""+e._wrapperState.initialValue:e.defaultValue!==""+n&&(e.defaultValue=""+n))}var Pn=Array.isArray;function tn(e,t,n,r){if(e=e.options,t){t={};for(var s=0;s<n.length;s++)t["$"+n[s]]=!0;for(n=0;n<e.length;n++)s=t.hasOwnProperty("$"+e[n].value),e[n].selected!==s&&(e[n].selected=s),s&&r&&(e[n].defaultSelected=!0)}else{for(n=""+yt(n),t=null,s=0;s<e.length;s++){if(e[s].value===n){e[s].selected=!0,r&&(e[s].defaultSelected=!0);return}t!==null||e[s].disabled||(t=e[s])}t!==null&&(t.selected=!0)}}function wl(e,t){if(t.dangerouslySetInnerHTML!=null)throw Error(S(91));return Q({},t,{value:void 0,defaultValue:void 0,children:""+e._wrapperState.initialValue})}function go(e,t){var n=t.value;if(n==null){if(n=t.children,t=t.defaultValue,n!=null){if(t!=null)throw Error(S(92));if(Pn(n)){if(1<n.length)throw Error(S(93));n=n[0]}t=n}t==null&&(t=""),n=t}e._wrapperState={initialValue:yt(n)}}function Wa(e,t){var n=yt(t.value),r=yt(t.defaultValue);n!=null&&(n=""+n,n!==e.value&&(e.value=n),t.defaultValue==null&&e.defaultValue!==n&&(e.defaultValue=n)),r!=null&&(e.defaultValue=""+r)}function xo(e){var t=e.textContent;t===e._wrapperState.initialValue&&t!==""&&t!==null&&(e.value=t)}function Qa(e){switch(e){case"svg":return"http://www.w3.org/2000/svg";case"math":return"http://www.w3.org/1998/Math/MathML";default:return"http://www.w3.org/1999/xhtml"}}function jl(e,t){return e==null||e==="http://www.w3.org/1999/xhtml"?Qa(t):e==="http://www.w3.org/2000/svg"&&t==="foreignObject"?"http://www.w3.org/1999/xhtml":e}var vr,Ka=function(e){return typeof MSApp<"u"&&MSApp.execUnsafeLocalFunction?function(t,n,r,s){MSApp.execUnsafeLocalFunction(function(){return e(t,n,r,s)})}:e}(function(e,t){if(e.namespaceURI!=="http://www.w3.org/2000/svg"||"innerHTML"in e)e.innerHTML=t;else{for(vr=vr||document.createElement("div"),vr.innerHTML="<svg>"+t.valueOf().toString()+"</svg>",t=vr.firstChild;e.firstChild;)e.removeChild(e.firstChild);for(;t.firstChild;)e.appendChild(t.firstChild)}});function Qn(e,t){if(t){var n=e.firstChild;if(n&&n===e.lastChild&&n.nodeType===3){n.nodeValue=t;return}}e.textContent=t}var In={animationIterationCount:!0,aspectRatio:!0,borderImageOutset:!0,borderImageSlice:!0,borderImageWidth:!0,boxFlex:!0,boxFlexGroup:!0,boxOrdinalGroup:!0,columnCount:!0,columns:!0,flex:!0,flexGrow:!0,flexPositive:!0,flexShrink:!0,flexNegative:!0,flexOrder:!0,gridArea:!0,gridRow:!0,gridRowEnd:!0,gridRowSpan:!0,gridRowStart:!0,gridColumn:!0,gridColumnEnd:!0,gridColumnSpan:!0,gridColumnStart:!0,fontWeight:!0,lineClamp:!0,lineHeight:!0,opacity:!0,order:!0,orphans:!0,tabSize:!0,widows:!0,zIndex:!0,zoom:!0,fillOpacity:!0,floodOpacity:!0,stopOpacity:!0,strokeDasharray:!0,strokeDashoffset:!0,strokeMiterlimit:!0,strokeOpacity:!0,strokeWidth:!0},kd=["Webkit","ms","Moz","O"];Object.keys(In).forEach(function(e){kd.forEach(function(t){t=t+e.charAt(0).toUpperCase()+e.substring(1),In[t]=In[e]})});function Ga(e,t,n){return t==null||typeof t=="boolean"||t===""?"":n||typeof t!="number"||t===0||In.hasOwnProperty(e)&&In[e]?(""+t).trim():t+"px"}function Ya(e,t){e=e.style;for(var n in t)if(t.hasOwnProperty(n)){var r=n.indexOf("--")===0,s=Ga(n,t[n],r);n==="float"&&(n="cssFloat"),r?e.setProperty(n,s):e[n]=s}}var Cd=Q({menuitem:!0},{area:!0,base:!0,br:!0,col:!0,embed:!0,hr:!0,img:!0,input:!0,keygen:!0,link:!0,meta:!0,param:!0,source:!0,track:!0,wbr:!0});function Nl(e,t){if(t){if(Cd[e]&&(t.children!=null||t.dangerouslySetInnerHTML!=null))throw Error(S(137,e));if(t.dangerouslySetInnerHTML!=null){if(t.children!=null)throw Error(S(60));if(typeof t.dangerouslySetInnerHTML!="object"||!("__html"in t.dangerouslySetInnerHTML))throw Error(S(61))}if(t.style!=null&&typeof t.style!="object")throw Error(S(62))}}function Sl(e,t){if(e.indexOf("-")===-1)return typeof t.is=="string";switch(e){case"annotation-xml":case"color-profile":case"font-face":case"font-face-src":case"font-face-uri":case"font-face-format":case"font-face-name":case"missing-glyph":return!1;default:return!0}}var kl=null;function ji(e){return e=e.target||e.srcElement||window,e.correspondingUseElement&&(e=e.correspondingUseElement),e.nodeType===3?e.parentNode:e}var Cl=null,nn=null,rn=null;function yo(e){if(e=fr(e)){if(typeof Cl!="function")throw Error(S(280));var t=e.stateNode;t&&(t=ks(t),Cl(e.stateNode,e.type,t))}}function Xa(e){nn?rn?rn.push(e):rn=[e]:nn=e}function Za(){if(nn){var e=nn,t=rn;if(rn=nn=null,yo(e),t)for(e=0;e<t.length;e++)yo(t[e])}}function Ja(e,t){return e(t)}function qa(){}var Vs=!1;function eu(e,t,n){if(Vs)return e(t,n);Vs=!0;try{return Ja(e,t,n)}finally{Vs=!1,(nn!==null||rn!==null)&&(qa(),Za())}}function Kn(e,t){var n=e.stateNode;if(n===null)return null;var r=ks(n);if(r===null)return null;n=r[t];e:switch(t){case"onClick":case"onClickCapture":case"onDoubleClick":case"onDoubleClickCapture":case"onMouseDown":case"onMouseDownCapture":case"onMouseMove":case"onMouseMoveCapture":case"onMouseUp":case"onMouseUpCapture":case"onMouseEnter":(r=!r.disabled)||(e=e.type,r=!(e==="button"||e==="input"||e==="select"||e==="textarea")),e=!r;break e;default:e=!1}if(e)return null;if(n&&typeof n!="function")throw Error(S(231,t,typeof n));return n}var El=!1;if(Ze)try{var Cn={};Object.defineProperty(Cn,"passive",{get:function(){El=!0}}),window.addEventListener("test",Cn,Cn),window.removeEventListener("test",Cn,Cn)}catch{El=!1}function Ed(e,t,n,r,s,i,o,a,u){var p=Array.prototype.slice.call(arguments,3);try{t.apply(n,p)}catch(h){this.onError(h)}}var Rn=!1,Kr=null,Gr=!1,Ll=null,Ld={onError:function(e){Rn=!0,Kr=e}};function bd(e,t,n,r,s,i,o,a,u){Rn=!1,Kr=null,Ed.apply(Ld,arguments)}function Md(e,t,n,r,s,i,o,a,u){if(bd.apply(this,arguments),Rn){if(Rn){var p=Kr;Rn=!1,Kr=null}else throw Error(S(198));Gr||(Gr=!0,Ll=p)}}function Ft(e){var t=e,n=e;if(e.alternate)for(;t.return;)t=t.return;else{e=t;do t=e,t.flags&4098&&(n=t.return),e=t.return;while(e)}return t.tag===3?n:null}function tu(e){if(e.tag===13){var t=e.memoizedState;if(t===null&&(e=e.alternate,e!==null&&(t=e.memoizedState)),t!==null)return t.dehydrated}return null}function vo(e){if(Ft(e)!==e)throw Error(S(188))}function Td(e){var t=e.alternate;if(!t){if(t=Ft(e),t===null)throw Error(S(188));return t!==e?null:e}for(var n=e,r=t;;){var s=n.return;if(s===null)break;var i=s.alternate;if(i===null){if(r=s.return,r!==null){n=r;continue}break}if(s.child===i.child){for(i=s.child;i;){if(i===n)return vo(s),e;if(i===r)return vo(s),t;i=i.sibling}throw Error(S(188))}if(n.return!==r.return)n=s,r=i;else{for(var o=!1,a=s.child;a;){if(a===n){o=!0,n=s,r=i;break}if(a===r){o=!0,r=s,n=i;break}a=a.sibling}if(!o){for(a=i.child;a;){if(a===n){o=!0,n=i,r=s;break}if(a===r){o=!0,r=i,n=s;break}a=a.sibling}if(!o)throw Error(S(189))}}if(n.alternate!==r)throw Error(S(190))}if(n.tag!==3)throw Error(S(188));return n.stateNode.current===n?e:t}function nu(e){return e=Td(e),e!==null?ru(e):null}function ru(e){if(e.tag===5||e.tag===6)return e;for(e=e.child;e!==null;){var t=ru(e);if(t!==null)return t;e=e.sibling}return null}var su=Ne.unstable_scheduleCallback,wo=Ne.unstable_cancelCallback,Dd=Ne.unstable_shouldYield,Ad=Ne.unstable_requestPaint,Y=Ne.unstable_now,Pd=Ne.unstable_getCurrentPriorityLevel,Ni=Ne.unstable_ImmediatePriority,lu=Ne.unstable_UserBlockingPriority,Yr=Ne.unstable_NormalPriority,_d=Ne.unstable_LowPriority,iu=Ne.unstable_IdlePriority,ws=null,He=null;function zd(e){if(He&&typeof He.onCommitFiberRoot=="function")try{He.onCommitFiberRoot(ws,e,void 0,(e.current.flags&128)===128)}catch{}}var Re=Math.clz32?Math.clz32:Od,Id=Math.log,Rd=Math.LN2;function Od(e){return e>>>=0,e===0?32:31-(Id(e)/Rd|0)|0}var wr=64,jr=4194304;function _n(e){switch(e&-e){case 1:return 1;case 2:return 2;case 4:return 4;case 8:return 8;case 16:return 16;case 32:return 32;case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return e&4194240;case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:return e&130023424;case 134217728:return 134217728;case 268435456:return 268435456;case 536870912:return 536870912;case 1073741824:return 1073741824;default:return e}}function Xr(e,t){var n=e.pendingLanes;if(n===0)return 0;var r=0,s=e.suspendedLanes,i=e.pingedLanes,o=n&268435455;if(o!==0){var a=o&~s;a!==0?r=_n(a):(i&=o,i!==0&&(r=_n(i)))}else o=n&~s,o!==0?r=_n(o):i!==0&&(r=_n(i));if(r===0)return 0;if(t!==0&&t!==r&&!(t&s)&&(s=r&-r,i=t&-t,s>=i||s===16&&(i&4194240)!==0))return t;if(r&4&&(r|=n&16),t=e.entangledLanes,t!==0)for(e=e.entanglements,t&=r;0<t;)n=31-Re(t),s=1<<n,r|=e[n],t&=~s;return r}function $d(e,t){switch(e){case 1:case 2:case 4:return t+250;case 8:case 16:case 32:case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return t+5e3;case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:return-1;case 134217728:case 268435456:case 536870912:case 1073741824:return-1;default:return-1}}function Ud(e,t){for(var n=e.suspendedLanes,r=e.pingedLanes,s=e.expirationTimes,i=e.pendingLanes;0<i;){var o=31-Re(i),a=1<<o,u=s[o];u===-1?(!(a&n)||a&r)&&(s[o]=$d(a,t)):u<=t&&(e.expiredLanes|=a),i&=~a}}function bl(e){return e=e.pendingLanes&-1073741825,e!==0?e:e&1073741824?1073741824:0}function ou(){var e=wr;return wr<<=1,!(wr&4194240)&&(wr=64),e}function Hs(e){for(var t=[],n=0;31>n;n++)t.push(e);return t}function cr(e,t,n){e.pendingLanes|=t,t!==536870912&&(e.suspendedLanes=0,e.pingedLanes=0),e=e.eventTimes,t=31-Re(t),e[t]=n}function Fd(e,t){var n=e.pendingLanes&~t;e.pendingLanes=t,e.suspendedLanes=0,e.pingedLanes=0,e.expiredLanes&=t,e.mutableReadLanes&=t,e.entangledLanes&=t,t=e.entanglements;var r=e.eventTimes;for(e=e.expirationTimes;0<n;){var s=31-Re(n),i=1<<s;t[s]=0,r[s]=-1,e[s]=-1,n&=~i}}function Si(e,t){var n=e.entangledLanes|=t;for(e=e.entanglements;n;){var r=31-Re(n),s=1<<r;s&t|e[r]&t&&(e[r]|=t),n&=~s}}var R=0;function au(e){return e&=-e,1<e?4<e?e&268435455?16:536870912:4:1}var uu,ki,cu,du,fu,Ml=!1,Nr=[],ut=null,ct=null,dt=null,Gn=new Map,Yn=new Map,lt=[],Vd="mousedown mouseup touchcancel touchend touchstart auxclick dblclick pointercancel pointerdown pointerup dragend dragstart drop compositionend compositionstart keydown keypress keyup input textInput copy cut paste click change contextmenu reset submit".split(" ");function jo(e,t){switch(e){case"focusin":case"focusout":ut=null;break;case"dragenter":case"dragleave":ct=null;break;case"mouseover":case"mouseout":dt=null;break;case"pointerover":case"pointerout":Gn.delete(t.pointerId);break;case"gotpointercapture":case"lostpointercapture":Yn.delete(t.pointerId)}}function En(e,t,n,r,s,i){return e===null||e.nativeEvent!==i?(e={blockedOn:t,domEventName:n,eventSystemFlags:r,nativeEvent:i,targetContainers:[s]},t!==null&&(t=fr(t),t!==null&&ki(t)),e):(e.eventSystemFlags|=r,t=e.targetContainers,s!==null&&t.indexOf(s)===-1&&t.push(s),e)}function Hd(e,t,n,r,s){switch(t){case"focusin":return ut=En(ut,e,t,n,r,s),!0;case"dragenter":return ct=En(ct,e,t,n,r,s),!0;case"mouseover":return dt=En(dt,e,t,n,r,s),!0;case"pointerover":var i=s.pointerId;return Gn.set(i,En(Gn.get(i)||null,e,t,n,r,s)),!0;case"gotpointercapture":return i=s.pointerId,Yn.set(i,En(Yn.get(i)||null,e,t,n,r,s)),!0}return!1}function pu(e){var t=Tt(e.target);if(t!==null){var n=Ft(t);if(n!==null){if(t=n.tag,t===13){if(t=tu(n),t!==null){e.blockedOn=t,fu(e.priority,function(){cu(n)});return}}else if(t===3&&n.stateNode.current.memoizedState.isDehydrated){e.blockedOn=n.tag===3?n.stateNode.containerInfo:null;return}}}e.blockedOn=null}function Ir(e){if(e.blockedOn!==null)return!1;for(var t=e.targetContainers;0<t.length;){var n=Tl(e.domEventName,e.eventSystemFlags,t[0],e.nativeEvent);if(n===null){n=e.nativeEvent;var r=new n.constructor(n.type,n);kl=r,n.target.dispatchEvent(r),kl=null}else return t=fr(n),t!==null&&ki(t),e.blockedOn=n,!1;t.shift()}return!0}function No(e,t,n){Ir(e)&&n.delete(t)}function Bd(){Ml=!1,ut!==null&&Ir(ut)&&(ut=null),ct!==null&&Ir(ct)&&(ct=null),dt!==null&&Ir(dt)&&(dt=null),Gn.forEach(No),Yn.forEach(No)}function Ln(e,t){e.blockedOn===t&&(e.blockedOn=null,Ml||(Ml=!0,Ne.unstable_scheduleCallback(Ne.unstable_NormalPriority,Bd)))}function Xn(e){function t(s){return Ln(s,e)}if(0<Nr.length){Ln(Nr[0],e);for(var n=1;n<Nr.length;n++){var r=Nr[n];r.blockedOn===e&&(r.blockedOn=null)}}for(ut!==null&&Ln(ut,e),ct!==null&&Ln(ct,e),dt!==null&&Ln(dt,e),Gn.forEach(t),Yn.forEach(t),n=0;n<lt.length;n++)r=lt[n],r.blockedOn===e&&(r.blockedOn=null);for(;0<lt.length&&(n=lt[0],n.blockedOn===null);)pu(n),n.blockedOn===null&&lt.shift()}var sn=tt.ReactCurrentBatchConfig,Zr=!0;function Wd(e,t,n,r){var s=R,i=sn.transition;sn.transition=null;try{R=1,Ci(e,t,n,r)}finally{R=s,sn.transition=i}}function Qd(e,t,n,r){var s=R,i=sn.transition;sn.transition=null;try{R=4,Ci(e,t,n,r)}finally{R=s,sn.transition=i}}function Ci(e,t,n,r){if(Zr){var s=Tl(e,t,n,r);if(s===null)qs(e,t,r,Jr,n),jo(e,r);else if(Hd(s,e,t,n,r))r.stopPropagation();else if(jo(e,r),t&4&&-1<Vd.indexOf(e)){for(;s!==null;){var i=fr(s);if(i!==null&&uu(i),i=Tl(e,t,n,r),i===null&&qs(e,t,r,Jr,n),i===s)break;s=i}s!==null&&r.stopPropagation()}else qs(e,t,r,null,n)}}var Jr=null;function Tl(e,t,n,r){if(Jr=null,e=ji(r),e=Tt(e),e!==null)if(t=Ft(e),t===null)e=null;else if(n=t.tag,n===13){if(e=tu(t),e!==null)return e;e=null}else if(n===3){if(t.stateNode.current.memoizedState.isDehydrated)return t.tag===3?t.stateNode.containerInfo:null;e=null}else t!==e&&(e=null);return Jr=e,null}function mu(e){switch(e){case"cancel":case"click":case"close":case"contextmenu":case"copy":case"cut":case"auxclick":case"dblclick":case"dragend":case"dragstart":case"drop":case"focusin":case"focusout":case"input":case"invalid":case"keydown":case"keypress":case"keyup":case"mousedown":case"mouseup":case"paste":case"pause":case"play":case"pointercancel":case"pointerdown":case"pointerup":case"ratechange":case"reset":case"resize":case"seeked":case"submit":case"touchcancel":case"touchend":case"touchstart":case"volumechange":case"change":case"selectionchange":case"textInput":case"compositionstart":case"compositionend":case"compositionupdate":case"beforeblur":case"afterblur":case"beforeinput":case"blur":case"fullscreenchange":case"focus":case"hashchange":case"popstate":case"select":case"selectstart":return 1;case"drag":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"mousemove":case"mouseout":case"mouseover":case"pointermove":case"pointerout":case"pointerover":case"scroll":case"toggle":case"touchmove":case"wheel":case"mouseenter":case"mouseleave":case"pointerenter":case"pointerleave":return 4;case"message":switch(Pd()){case Ni:return 1;case lu:return 4;case Yr:case _d:return 16;case iu:return 536870912;default:return 16}default:return 16}}var ot=null,Ei=null,Rr=null;function hu(){if(Rr)return Rr;var e,t=Ei,n=t.length,r,s="value"in ot?ot.value:ot.textContent,i=s.length;for(e=0;e<n&&t[e]===s[e];e++);var o=n-e;for(r=1;r<=o&&t[n-r]===s[i-r];r++);return Rr=s.slice(e,1<r?1-r:void 0)}function Or(e){var t=e.keyCode;return"charCode"in e?(e=e.charCode,e===0&&t===13&&(e=13)):e=t,e===10&&(e=13),32<=e||e===13?e:0}function Sr(){return!0}function So(){return!1}function ke(e){function t(n,r,s,i,o){this._reactName=n,this._targetInst=s,this.type=r,this.nativeEvent=i,this.target=o,this.currentTarget=null;for(var a in e)e.hasOwnProperty(a)&&(n=e[a],this[a]=n?n(i):i[a]);return this.isDefaultPrevented=(i.defaultPrevented!=null?i.defaultPrevented:i.returnValue===!1)?Sr:So,this.isPropagationStopped=So,this}return Q(t.prototype,{preventDefault:function(){this.defaultPrevented=!0;var n=this.nativeEvent;n&&(n.preventDefault?n.preventDefault():typeof n.returnValue!="unknown"&&(n.returnValue=!1),this.isDefaultPrevented=Sr)},stopPropagation:function(){var n=this.nativeEvent;n&&(n.stopPropagation?n.stopPropagation():typeof n.cancelBubble!="unknown"&&(n.cancelBubble=!0),this.isPropagationStopped=Sr)},persist:function(){},isPersistent:Sr}),t}var vn={eventPhase:0,bubbles:0,cancelable:0,timeStamp:function(e){return e.timeStamp||Date.now()},defaultPrevented:0,isTrusted:0},Li=ke(vn),dr=Q({},vn,{view:0,detail:0}),Kd=ke(dr),Bs,Ws,bn,js=Q({},dr,{screenX:0,screenY:0,clientX:0,clientY:0,pageX:0,pageY:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,getModifierState:bi,button:0,buttons:0,relatedTarget:function(e){return e.relatedTarget===void 0?e.fromElement===e.srcElement?e.toElement:e.fromElement:e.relatedTarget},movementX:function(e){return"movementX"in e?e.movementX:(e!==bn&&(bn&&e.type==="mousemove"?(Bs=e.screenX-bn.screenX,Ws=e.screenY-bn.screenY):Ws=Bs=0,bn=e),Bs)},movementY:function(e){return"movementY"in e?e.movementY:Ws}}),ko=ke(js),Gd=Q({},js,{dataTransfer:0}),Yd=ke(Gd),Xd=Q({},dr,{relatedTarget:0}),Qs=ke(Xd),Zd=Q({},vn,{animationName:0,elapsedTime:0,pseudoElement:0}),Jd=ke(Zd),qd=Q({},vn,{clipboardData:function(e){return"clipboardData"in e?e.clipboardData:window.clipboardData}}),ef=ke(qd),tf=Q({},vn,{data:0}),Co=ke(tf),nf={Esc:"Escape",Spacebar:" ",Left:"ArrowLeft",Up:"ArrowUp",Right:"ArrowRight",Down:"ArrowDown",Del:"Delete",Win:"OS",Menu:"ContextMenu",Apps:"ContextMenu",Scroll:"ScrollLock",MozPrintableKey:"Unidentified"},rf={8:"Backspace",9:"Tab",12:"Clear",13:"Enter",16:"Shift",17:"Control",18:"Alt",19:"Pause",20:"CapsLock",27:"Escape",32:" ",33:"PageUp",34:"PageDown",35:"End",36:"Home",37:"ArrowLeft",38:"ArrowUp",39:"ArrowRight",40:"ArrowDown",45:"Insert",46:"Delete",112:"F1",113:"F2",114:"F3",115:"F4",116:"F5",117:"F6",118:"F7",119:"F8",120:"F9",121:"F10",122:"F11",123:"F12",144:"NumLock",145:"ScrollLock",224:"Meta"},sf={Alt:"altKey",Control:"ctrlKey",Meta:"metaKey",Shift:"shiftKey"};function lf(e){var t=this.nativeEvent;return t.getModifierState?t.getModifierState(e):(e=sf[e])?!!t[e]:!1}function bi(){return lf}var of=Q({},dr,{key:function(e){if(e.key){var t=nf[e.key]||e.key;if(t!=="Unidentified")return t}return e.type==="keypress"?(e=Or(e),e===13?"Enter":String.fromCharCode(e)):e.type==="keydown"||e.type==="keyup"?rf[e.keyCode]||"Unidentified":""},code:0,location:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,repeat:0,locale:0,getModifierState:bi,charCode:function(e){return e.type==="keypress"?Or(e):0},keyCode:function(e){return e.type==="keydown"||e.type==="keyup"?e.keyCode:0},which:function(e){return e.type==="keypress"?Or(e):e.type==="keydown"||e.type==="keyup"?e.keyCode:0}}),af=ke(of),uf=Q({},js,{pointerId:0,width:0,height:0,pressure:0,tangentialPressure:0,tiltX:0,tiltY:0,twist:0,pointerType:0,isPrimary:0}),Eo=ke(uf),cf=Q({},dr,{touches:0,targetTouches:0,changedTouches:0,altKey:0,metaKey:0,ctrlKey:0,shiftKey:0,getModifierState:bi}),df=ke(cf),ff=Q({},vn,{propertyName:0,elapsedTime:0,pseudoElement:0}),pf=ke(ff),mf=Q({},js,{deltaX:function(e){return"deltaX"in e?e.deltaX:"wheelDeltaX"in e?-e.wheelDeltaX:0},deltaY:function(e){return"deltaY"in e?e.deltaY:"wheelDeltaY"in e?-e.wheelDeltaY:"wheelDelta"in e?-e.wheelDelta:0},deltaZ:0,deltaMode:0}),hf=ke(mf),gf=[9,13,27,32],Mi=Ze&&"CompositionEvent"in window,On=null;Ze&&"documentMode"in document&&(On=document.documentMode);var xf=Ze&&"TextEvent"in window&&!On,gu=Ze&&(!Mi||On&&8<On&&11>=On),Lo=String.fromCharCode(32),bo=!1;function xu(e,t){switch(e){case"keyup":return gf.indexOf(t.keyCode)!==-1;case"keydown":return t.keyCode!==229;case"keypress":case"mousedown":case"focusout":return!0;default:return!1}}function yu(e){return e=e.detail,typeof e=="object"&&"data"in e?e.data:null}var Wt=!1;function yf(e,t){switch(e){case"compositionend":return yu(t);case"keypress":return t.which!==32?null:(bo=!0,Lo);case"textInput":return e=t.data,e===Lo&&bo?null:e;default:return null}}function vf(e,t){if(Wt)return e==="compositionend"||!Mi&&xu(e,t)?(e=hu(),Rr=Ei=ot=null,Wt=!1,e):null;switch(e){case"paste":return null;case"keypress":if(!(t.ctrlKey||t.altKey||t.metaKey)||t.ctrlKey&&t.altKey){if(t.char&&1<t.char.length)return t.char;if(t.which)return String.fromCharCode(t.which)}return null;case"compositionend":return gu&&t.locale!=="ko"?null:t.data;default:return null}}var wf={color:!0,date:!0,datetime:!0,"datetime-local":!0,email:!0,month:!0,number:!0,password:!0,range:!0,search:!0,tel:!0,text:!0,time:!0,url:!0,week:!0};function Mo(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return t==="input"?!!wf[e.type]:t==="textarea"}function vu(e,t,n,r){Xa(r),t=qr(t,"onChange"),0<t.length&&(n=new Li("onChange","change",null,n,r),e.push({event:n,listeners:t}))}var $n=null,Zn=null;function jf(e){Tu(e,0)}function Ns(e){var t=Gt(e);if(Ha(t))return e}function Nf(e,t){if(e==="change")return t}var wu=!1;if(Ze){var Ks;if(Ze){var Gs="oninput"in document;if(!Gs){var To=document.createElement("div");To.setAttribute("oninput","return;"),Gs=typeof To.oninput=="function"}Ks=Gs}else Ks=!1;wu=Ks&&(!document.documentMode||9<document.documentMode)}function Do(){$n&&($n.detachEvent("onpropertychange",ju),Zn=$n=null)}function ju(e){if(e.propertyName==="value"&&Ns(Zn)){var t=[];vu(t,Zn,e,ji(e)),eu(jf,t)}}function Sf(e,t,n){e==="focusin"?(Do(),$n=t,Zn=n,$n.attachEvent("onpropertychange",ju)):e==="focusout"&&Do()}function kf(e){if(e==="selectionchange"||e==="keyup"||e==="keydown")return Ns(Zn)}function Cf(e,t){if(e==="click")return Ns(t)}function Ef(e,t){if(e==="input"||e==="change")return Ns(t)}function Lf(e,t){return e===t&&(e!==0||1/e===1/t)||e!==e&&t!==t}var $e=typeof Object.is=="function"?Object.is:Lf;function Jn(e,t){if($e(e,t))return!0;if(typeof e!="object"||e===null||typeof t!="object"||t===null)return!1;var n=Object.keys(e),r=Object.keys(t);if(n.length!==r.length)return!1;for(r=0;r<n.length;r++){var s=n[r];if(!fl.call(t,s)||!$e(e[s],t[s]))return!1}return!0}function Ao(e){for(;e&&e.firstChild;)e=e.firstChild;return e}function Po(e,t){var n=Ao(e);e=0;for(var r;n;){if(n.nodeType===3){if(r=e+n.textContent.length,e<=t&&r>=t)return{node:n,offset:t-e};e=r}e:{for(;n;){if(n.nextSibling){n=n.nextSibling;break e}n=n.parentNode}n=void 0}n=Ao(n)}}function Nu(e,t){return e&&t?e===t?!0:e&&e.nodeType===3?!1:t&&t.nodeType===3?Nu(e,t.parentNode):"contains"in e?e.contains(t):e.compareDocumentPosition?!!(e.compareDocumentPosition(t)&16):!1:!1}function Su(){for(var e=window,t=Qr();t instanceof e.HTMLIFrameElement;){try{var n=typeof t.contentWindow.location.href=="string"}catch{n=!1}if(n)e=t.contentWindow;else break;t=Qr(e.document)}return t}function Ti(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return t&&(t==="input"&&(e.type==="text"||e.type==="search"||e.type==="tel"||e.type==="url"||e.type==="password")||t==="textarea"||e.contentEditable==="true")}function bf(e){var t=Su(),n=e.focusedElem,r=e.selectionRange;if(t!==n&&n&&n.ownerDocument&&Nu(n.ownerDocument.documentElement,n)){if(r!==null&&Ti(n)){if(t=r.start,e=r.end,e===void 0&&(e=t),"selectionStart"in n)n.selectionStart=t,n.selectionEnd=Math.min(e,n.value.length);else if(e=(t=n.ownerDocument||document)&&t.defaultView||window,e.getSelection){e=e.getSelection();var s=n.textContent.length,i=Math.min(r.start,s);r=r.end===void 0?i:Math.min(r.end,s),!e.extend&&i>r&&(s=r,r=i,i=s),s=Po(n,i);var o=Po(n,r);s&&o&&(e.rangeCount!==1||e.anchorNode!==s.node||e.anchorOffset!==s.offset||e.focusNode!==o.node||e.focusOffset!==o.offset)&&(t=t.createRange(),t.setStart(s.node,s.offset),e.removeAllRanges(),i>r?(e.addRange(t),e.extend(o.node,o.offset)):(t.setEnd(o.node,o.offset),e.addRange(t)))}}for(t=[],e=n;e=e.parentNode;)e.nodeType===1&&t.push({element:e,left:e.scrollLeft,top:e.scrollTop});for(typeof n.focus=="function"&&n.focus(),n=0;n<t.length;n++)e=t[n],e.element.scrollLeft=e.left,e.element.scrollTop=e.top}}var Mf=Ze&&"documentMode"in document&&11>=document.documentMode,Qt=null,Dl=null,Un=null,Al=!1;function _o(e,t,n){var r=n.window===n?n.document:n.nodeType===9?n:n.ownerDocument;Al||Qt==null||Qt!==Qr(r)||(r=Qt,"selectionStart"in r&&Ti(r)?r={start:r.selectionStart,end:r.selectionEnd}:(r=(r.ownerDocument&&r.ownerDocument.defaultView||window).getSelection(),r={anchorNode:r.anchorNode,anchorOffset:r.anchorOffset,focusNode:r.focusNode,focusOffset:r.focusOffset}),Un&&Jn(Un,r)||(Un=r,r=qr(Dl,"onSelect"),0<r.length&&(t=new Li("onSelect","select",null,t,n),e.push({event:t,listeners:r}),t.target=Qt)))}function kr(e,t){var n={};return n[e.toLowerCase()]=t.toLowerCase(),n["Webkit"+e]="webkit"+t,n["Moz"+e]="moz"+t,n}var Kt={animationend:kr("Animation","AnimationEnd"),animationiteration:kr("Animation","AnimationIteration"),animationstart:kr("Animation","AnimationStart"),transitionend:kr("Transition","TransitionEnd")},Ys={},ku={};Ze&&(ku=document.createElement("div").style,"AnimationEvent"in window||(delete Kt.animationend.animation,delete Kt.animationiteration.animation,delete Kt.animationstart.animation),"TransitionEvent"in window||delete Kt.transitionend.transition);function Ss(e){if(Ys[e])return Ys[e];if(!Kt[e])return e;var t=Kt[e],n;for(n in t)if(t.hasOwnProperty(n)&&n in ku)return Ys[e]=t[n];return e}var Cu=Ss("animationend"),Eu=Ss("animationiteration"),Lu=Ss("animationstart"),bu=Ss("transitionend"),Mu=new Map,zo="abort auxClick cancel canPlay canPlayThrough click close contextMenu copy cut drag dragEnd dragEnter dragExit dragLeave dragOver dragStart drop durationChange emptied encrypted ended error gotPointerCapture input invalid keyDown keyPress keyUp load loadedData loadedMetadata loadStart lostPointerCapture mouseDown mouseMove mouseOut mouseOver mouseUp paste pause play playing pointerCancel pointerDown pointerMove pointerOut pointerOver pointerUp progress rateChange reset resize seeked seeking stalled submit suspend timeUpdate touchCancel touchEnd touchStart volumeChange scroll toggle touchMove waiting wheel".split(" ");function wt(e,t){Mu.set(e,t),Ut(t,[e])}for(var Xs=0;Xs<zo.length;Xs++){var Zs=zo[Xs],Tf=Zs.toLowerCase(),Df=Zs[0].toUpperCase()+Zs.slice(1);wt(Tf,"on"+Df)}wt(Cu,"onAnimationEnd");wt(Eu,"onAnimationIteration");wt(Lu,"onAnimationStart");wt("dblclick","onDoubleClick");wt("focusin","onFocus");wt("focusout","onBlur");wt(bu,"onTransitionEnd");an("onMouseEnter",["mouseout","mouseover"]);an("onMouseLeave",["mouseout","mouseover"]);an("onPointerEnter",["pointerout","pointerover"]);an("onPointerLeave",["pointerout","pointerover"]);Ut("onChange","change click focusin focusout input keydown keyup selectionchange".split(" "));Ut("onSelect","focusout contextmenu dragend focusin keydown keyup mousedown mouseup selectionchange".split(" "));Ut("onBeforeInput",["compositionend","keypress","textInput","paste"]);Ut("onCompositionEnd","compositionend focusout keydown keypress keyup mousedown".split(" "));Ut("onCompositionStart","compositionstart focusout keydown keypress keyup mousedown".split(" "));Ut("onCompositionUpdate","compositionupdate focusout keydown keypress keyup mousedown".split(" "));var zn="abort canplay canplaythrough durationchange emptied encrypted ended error loadeddata loadedmetadata loadstart pause play playing progress ratechange resize seeked seeking stalled suspend timeupdate volumechange waiting".split(" "),Af=new Set("cancel close invalid load scroll toggle".split(" ").concat(zn));function Io(e,t,n){var r=e.type||"unknown-event";e.currentTarget=n,Md(r,t,void 0,e),e.currentTarget=null}function Tu(e,t){t=(t&4)!==0;for(var n=0;n<e.length;n++){var r=e[n],s=r.event;r=r.listeners;e:{var i=void 0;if(t)for(var o=r.length-1;0<=o;o--){var a=r[o],u=a.instance,p=a.currentTarget;if(a=a.listener,u!==i&&s.isPropagationStopped())break e;Io(s,a,p),i=u}else for(o=0;o<r.length;o++){if(a=r[o],u=a.instance,p=a.currentTarget,a=a.listener,u!==i&&s.isPropagationStopped())break e;Io(s,a,p),i=u}}}if(Gr)throw e=Ll,Gr=!1,Ll=null,e}function F(e,t){var n=t[Rl];n===void 0&&(n=t[Rl]=new Set);var r=e+"__bubble";n.has(r)||(Du(t,e,2,!1),n.add(r))}function Js(e,t,n){var r=0;t&&(r|=4),Du(n,e,r,t)}var Cr="_reactListening"+Math.random().toString(36).slice(2);function qn(e){if(!e[Cr]){e[Cr]=!0,Oa.forEach(function(n){n!=="selectionchange"&&(Af.has(n)||Js(n,!1,e),Js(n,!0,e))});var t=e.nodeType===9?e:e.ownerDocument;t===null||t[Cr]||(t[Cr]=!0,Js("selectionchange",!1,t))}}function Du(e,t,n,r){switch(mu(t)){case 1:var s=Wd;break;case 4:s=Qd;break;default:s=Ci}n=s.bind(null,t,n,e),s=void 0,!El||t!=="touchstart"&&t!=="touchmove"&&t!=="wheel"||(s=!0),r?s!==void 0?e.addEventListener(t,n,{capture:!0,passive:s}):e.addEventListener(t,n,!0):s!==void 0?e.addEventListener(t,n,{passive:s}):e.addEventListener(t,n,!1)}function qs(e,t,n,r,s){var i=r;if(!(t&1)&&!(t&2)&&r!==null)e:for(;;){if(r===null)return;var o=r.tag;if(o===3||o===4){var a=r.stateNode.containerInfo;if(a===s||a.nodeType===8&&a.parentNode===s)break;if(o===4)for(o=r.return;o!==null;){var u=o.tag;if((u===3||u===4)&&(u=o.stateNode.containerInfo,u===s||u.nodeType===8&&u.parentNode===s))return;o=o.return}for(;a!==null;){if(o=Tt(a),o===null)return;if(u=o.tag,u===5||u===6){r=i=o;continue e}a=a.parentNode}}r=r.return}eu(function(){var p=i,h=ji(n),m=[];e:{var x=Mu.get(e);if(x!==void 0){var y=Li,w=e;switch(e){case"keypress":if(Or(n)===0)break e;case"keydown":case"keyup":y=af;break;case"focusin":w="focus",y=Qs;break;case"focusout":w="blur",y=Qs;break;case"beforeblur":case"afterblur":y=Qs;break;case"click":if(n.button===2)break e;case"auxclick":case"dblclick":case"mousedown":case"mousemove":case"mouseup":case"mouseout":case"mouseover":case"contextmenu":y=ko;break;case"drag":case"dragend":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"dragstart":case"drop":y=Yd;break;case"touchcancel":case"touchend":case"touchmove":case"touchstart":y=df;break;case Cu:case Eu:case Lu:y=Jd;break;case bu:y=pf;break;case"scroll":y=Kd;break;case"wheel":y=hf;break;case"copy":case"cut":case"paste":y=ef;break;case"gotpointercapture":case"lostpointercapture":case"pointercancel":case"pointerdown":case"pointermove":case"pointerout":case"pointerover":case"pointerup":y=Eo}var v=(t&4)!==0,N=!v&&e==="scroll",d=v?x!==null?x+"Capture":null:x;v=[];for(var c=p,f;c!==null;){f=c;var g=f.stateNode;if(f.tag===5&&g!==null&&(f=g,d!==null&&(g=Kn(c,d),g!=null&&v.push(er(c,g,f)))),N)break;c=c.return}0<v.length&&(x=new y(x,w,null,n,h),m.push({event:x,listeners:v}))}}if(!(t&7)){e:{if(x=e==="mouseover"||e==="pointerover",y=e==="mouseout"||e==="pointerout",x&&n!==kl&&(w=n.relatedTarget||n.fromElement)&&(Tt(w)||w[Je]))break e;if((y||x)&&(x=h.window===h?h:(x=h.ownerDocument)?x.defaultView||x.parentWindow:window,y?(w=n.relatedTarget||n.toElement,y=p,w=w?Tt(w):null,w!==null&&(N=Ft(w),w!==N||w.tag!==5&&w.tag!==6)&&(w=null)):(y=null,w=p),y!==w)){if(v=ko,g="onMouseLeave",d="onMouseEnter",c="mouse",(e==="pointerout"||e==="pointerover")&&(v=Eo,g="onPointerLeave",d="onPointerEnter",c="pointer"),N=y==null?x:Gt(y),f=w==null?x:Gt(w),x=new v(g,c+"leave",y,n,h),x.target=N,x.relatedTarget=f,g=null,Tt(h)===p&&(v=new v(d,c+"enter",w,n,h),v.target=f,v.relatedTarget=N,g=v),N=g,y&&w)t:{for(v=y,d=w,c=0,f=v;f;f=Vt(f))c++;for(f=0,g=d;g;g=Vt(g))f++;for(;0<c-f;)v=Vt(v),c--;for(;0<f-c;)d=Vt(d),f--;for(;c--;){if(v===d||d!==null&&v===d.alternate)break t;v=Vt(v),d=Vt(d)}v=null}else v=null;y!==null&&Ro(m,x,y,v,!1),w!==null&&N!==null&&Ro(m,N,w,v,!0)}}e:{if(x=p?Gt(p):window,y=x.nodeName&&x.nodeName.toLowerCase(),y==="select"||y==="input"&&x.type==="file")var j=Nf;else if(Mo(x))if(wu)j=Ef;else{j=kf;var k=Sf}else(y=x.nodeName)&&y.toLowerCase()==="input"&&(x.type==="checkbox"||x.type==="radio")&&(j=Cf);if(j&&(j=j(e,p))){vu(m,j,n,h);break e}k&&k(e,x,p),e==="focusout"&&(k=x._wrapperState)&&k.controlled&&x.type==="number"&&vl(x,"number",x.value)}switch(k=p?Gt(p):window,e){case"focusin":(Mo(k)||k.contentEditable==="true")&&(Qt=k,Dl=p,Un=null);break;case"focusout":Un=Dl=Qt=null;break;case"mousedown":Al=!0;break;case"contextmenu":case"mouseup":case"dragend":Al=!1,_o(m,n,h);break;case"selectionchange":if(Mf)break;case"keydown":case"keyup":_o(m,n,h)}var L;if(Mi)e:{switch(e){case"compositionstart":var b="onCompositionStart";break e;case"compositionend":b="onCompositionEnd";break e;case"compositionupdate":b="onCompositionUpdate";break e}b=void 0}else Wt?xu(e,n)&&(b="onCompositionEnd"):e==="keydown"&&n.keyCode===229&&(b="onCompositionStart");b&&(gu&&n.locale!=="ko"&&(Wt||b!=="onCompositionStart"?b==="onCompositionEnd"&&Wt&&(L=hu()):(ot=h,Ei="value"in ot?ot.value:ot.textContent,Wt=!0)),k=qr(p,b),0<k.length&&(b=new Co(b,e,null,n,h),m.push({event:b,listeners:k}),L?b.data=L:(L=yu(n),L!==null&&(b.data=L)))),(L=xf?yf(e,n):vf(e,n))&&(p=qr(p,"onBeforeInput"),0<p.length&&(h=new Co("onBeforeInput","beforeinput",null,n,h),m.push({event:h,listeners:p}),h.data=L))}Tu(m,t)})}function er(e,t,n){return{instance:e,listener:t,currentTarget:n}}function qr(e,t){for(var n=t+"Capture",r=[];e!==null;){var s=e,i=s.stateNode;s.tag===5&&i!==null&&(s=i,i=Kn(e,n),i!=null&&r.unshift(er(e,i,s)),i=Kn(e,t),i!=null&&r.push(er(e,i,s))),e=e.return}return r}function Vt(e){if(e===null)return null;do e=e.return;while(e&&e.tag!==5);return e||null}function Ro(e,t,n,r,s){for(var i=t._reactName,o=[];n!==null&&n!==r;){var a=n,u=a.alternate,p=a.stateNode;if(u!==null&&u===r)break;a.tag===5&&p!==null&&(a=p,s?(u=Kn(n,i),u!=null&&o.unshift(er(n,u,a))):s||(u=Kn(n,i),u!=null&&o.push(er(n,u,a)))),n=n.return}o.length!==0&&e.push({event:t,listeners:o})}var Pf=/\r\n?/g,_f=/\u0000|\uFFFD/g;function Oo(e){return(typeof e=="string"?e:""+e).replace(Pf,`
`).replace(_f,"")}function Er(e,t,n){if(t=Oo(t),Oo(e)!==t&&n)throw Error(S(425))}function es(){}var Pl=null,_l=null;function zl(e,t){return e==="textarea"||e==="noscript"||typeof t.children=="string"||typeof t.children=="number"||typeof t.dangerouslySetInnerHTML=="object"&&t.dangerouslySetInnerHTML!==null&&t.dangerouslySetInnerHTML.__html!=null}var Il=typeof setTimeout=="function"?setTimeout:void 0,zf=typeof clearTimeout=="function"?clearTimeout:void 0,$o=typeof Promise=="function"?Promise:void 0,If=typeof queueMicrotask=="function"?queueMicrotask:typeof $o<"u"?function(e){return $o.resolve(null).then(e).catch(Rf)}:Il;function Rf(e){setTimeout(function(){throw e})}function el(e,t){var n=t,r=0;do{var s=n.nextSibling;if(e.removeChild(n),s&&s.nodeType===8)if(n=s.data,n==="/$"){if(r===0){e.removeChild(s),Xn(t);return}r--}else n!=="$"&&n!=="$?"&&n!=="$!"||r++;n=s}while(n);Xn(t)}function ft(e){for(;e!=null;e=e.nextSibling){var t=e.nodeType;if(t===1||t===3)break;if(t===8){if(t=e.data,t==="$"||t==="$!"||t==="$?")break;if(t==="/$")return null}}return e}function Uo(e){e=e.previousSibling;for(var t=0;e;){if(e.nodeType===8){var n=e.data;if(n==="$"||n==="$!"||n==="$?"){if(t===0)return e;t--}else n==="/$"&&t++}e=e.previousSibling}return null}var wn=Math.random().toString(36).slice(2),Ve="__reactFiber$"+wn,tr="__reactProps$"+wn,Je="__reactContainer$"+wn,Rl="__reactEvents$"+wn,Of="__reactListeners$"+wn,$f="__reactHandles$"+wn;function Tt(e){var t=e[Ve];if(t)return t;for(var n=e.parentNode;n;){if(t=n[Je]||n[Ve]){if(n=t.alternate,t.child!==null||n!==null&&n.child!==null)for(e=Uo(e);e!==null;){if(n=e[Ve])return n;e=Uo(e)}return t}e=n,n=e.parentNode}return null}function fr(e){return e=e[Ve]||e[Je],!e||e.tag!==5&&e.tag!==6&&e.tag!==13&&e.tag!==3?null:e}function Gt(e){if(e.tag===5||e.tag===6)return e.stateNode;throw Error(S(33))}function ks(e){return e[tr]||null}var Ol=[],Yt=-1;function jt(e){return{current:e}}function V(e){0>Yt||(e.current=Ol[Yt],Ol[Yt]=null,Yt--)}function U(e,t){Yt++,Ol[Yt]=e.current,e.current=t}var vt={},ue=jt(vt),ge=jt(!1),zt=vt;function un(e,t){var n=e.type.contextTypes;if(!n)return vt;var r=e.stateNode;if(r&&r.__reactInternalMemoizedUnmaskedChildContext===t)return r.__reactInternalMemoizedMaskedChildContext;var s={},i;for(i in n)s[i]=t[i];return r&&(e=e.stateNode,e.__reactInternalMemoizedUnmaskedChildContext=t,e.__reactInternalMemoizedMaskedChildContext=s),s}function xe(e){return e=e.childContextTypes,e!=null}function ts(){V(ge),V(ue)}function Fo(e,t,n){if(ue.current!==vt)throw Error(S(168));U(ue,t),U(ge,n)}function Au(e,t,n){var r=e.stateNode;if(t=t.childContextTypes,typeof r.getChildContext!="function")return n;r=r.getChildContext();for(var s in r)if(!(s in t))throw Error(S(108,Nd(e)||"Unknown",s));return Q({},n,r)}function ns(e){return e=(e=e.stateNode)&&e.__reactInternalMemoizedMergedChildContext||vt,zt=ue.current,U(ue,e),U(ge,ge.current),!0}function Vo(e,t,n){var r=e.stateNode;if(!r)throw Error(S(169));n?(e=Au(e,t,zt),r.__reactInternalMemoizedMergedChildContext=e,V(ge),V(ue),U(ue,e)):V(ge),U(ge,n)}var Qe=null,Cs=!1,tl=!1;function Pu(e){Qe===null?Qe=[e]:Qe.push(e)}function Uf(e){Cs=!0,Pu(e)}function Nt(){if(!tl&&Qe!==null){tl=!0;var e=0,t=R;try{var n=Qe;for(R=1;e<n.length;e++){var r=n[e];do r=r(!0);while(r!==null)}Qe=null,Cs=!1}catch(s){throw Qe!==null&&(Qe=Qe.slice(e+1)),su(Ni,Nt),s}finally{R=t,tl=!1}}return null}var Xt=[],Zt=0,rs=null,ss=0,Ee=[],Le=0,It=null,Ke=1,Ge="";function Et(e,t){Xt[Zt++]=ss,Xt[Zt++]=rs,rs=e,ss=t}function _u(e,t,n){Ee[Le++]=Ke,Ee[Le++]=Ge,Ee[Le++]=It,It=e;var r=Ke;e=Ge;var s=32-Re(r)-1;r&=~(1<<s),n+=1;var i=32-Re(t)+s;if(30<i){var o=s-s%5;i=(r&(1<<o)-1).toString(32),r>>=o,s-=o,Ke=1<<32-Re(t)+s|n<<s|r,Ge=i+e}else Ke=1<<i|n<<s|r,Ge=e}function Di(e){e.return!==null&&(Et(e,1),_u(e,1,0))}function Ai(e){for(;e===rs;)rs=Xt[--Zt],Xt[Zt]=null,ss=Xt[--Zt],Xt[Zt]=null;for(;e===It;)It=Ee[--Le],Ee[Le]=null,Ge=Ee[--Le],Ee[Le]=null,Ke=Ee[--Le],Ee[Le]=null}var je=null,we=null,H=!1,Ie=null;function zu(e,t){var n=be(5,null,null,0);n.elementType="DELETED",n.stateNode=t,n.return=e,t=e.deletions,t===null?(e.deletions=[n],e.flags|=16):t.push(n)}function Ho(e,t){switch(e.tag){case 5:var n=e.type;return t=t.nodeType!==1||n.toLowerCase()!==t.nodeName.toLowerCase()?null:t,t!==null?(e.stateNode=t,je=e,we=ft(t.firstChild),!0):!1;case 6:return t=e.pendingProps===""||t.nodeType!==3?null:t,t!==null?(e.stateNode=t,je=e,we=null,!0):!1;case 13:return t=t.nodeType!==8?null:t,t!==null?(n=It!==null?{id:Ke,overflow:Ge}:null,e.memoizedState={dehydrated:t,treeContext:n,retryLane:1073741824},n=be(18,null,null,0),n.stateNode=t,n.return=e,e.child=n,je=e,we=null,!0):!1;default:return!1}}function $l(e){return(e.mode&1)!==0&&(e.flags&128)===0}function Ul(e){if(H){var t=we;if(t){var n=t;if(!Ho(e,t)){if($l(e))throw Error(S(418));t=ft(n.nextSibling);var r=je;t&&Ho(e,t)?zu(r,n):(e.flags=e.flags&-4097|2,H=!1,je=e)}}else{if($l(e))throw Error(S(418));e.flags=e.flags&-4097|2,H=!1,je=e}}}function Bo(e){for(e=e.return;e!==null&&e.tag!==5&&e.tag!==3&&e.tag!==13;)e=e.return;je=e}function Lr(e){if(e!==je)return!1;if(!H)return Bo(e),H=!0,!1;var t;if((t=e.tag!==3)&&!(t=e.tag!==5)&&(t=e.type,t=t!=="head"&&t!=="body"&&!zl(e.type,e.memoizedProps)),t&&(t=we)){if($l(e))throw Iu(),Error(S(418));for(;t;)zu(e,t),t=ft(t.nextSibling)}if(Bo(e),e.tag===13){if(e=e.memoizedState,e=e!==null?e.dehydrated:null,!e)throw Error(S(317));e:{for(e=e.nextSibling,t=0;e;){if(e.nodeType===8){var n=e.data;if(n==="/$"){if(t===0){we=ft(e.nextSibling);break e}t--}else n!=="$"&&n!=="$!"&&n!=="$?"||t++}e=e.nextSibling}we=null}}else we=je?ft(e.stateNode.nextSibling):null;return!0}function Iu(){for(var e=we;e;)e=ft(e.nextSibling)}function cn(){we=je=null,H=!1}function Pi(e){Ie===null?Ie=[e]:Ie.push(e)}var Ff=tt.ReactCurrentBatchConfig;function Mn(e,t,n){if(e=n.ref,e!==null&&typeof e!="function"&&typeof e!="object"){if(n._owner){if(n=n._owner,n){if(n.tag!==1)throw Error(S(309));var r=n.stateNode}if(!r)throw Error(S(147,e));var s=r,i=""+e;return t!==null&&t.ref!==null&&typeof t.ref=="function"&&t.ref._stringRef===i?t.ref:(t=function(o){var a=s.refs;o===null?delete a[i]:a[i]=o},t._stringRef=i,t)}if(typeof e!="string")throw Error(S(284));if(!n._owner)throw Error(S(290,e))}return e}function br(e,t){throw e=Object.prototype.toString.call(t),Error(S(31,e==="[object Object]"?"object with keys {"+Object.keys(t).join(", ")+"}":e))}function Wo(e){var t=e._init;return t(e._payload)}function Ru(e){function t(d,c){if(e){var f=d.deletions;f===null?(d.deletions=[c],d.flags|=16):f.push(c)}}function n(d,c){if(!e)return null;for(;c!==null;)t(d,c),c=c.sibling;return null}function r(d,c){for(d=new Map;c!==null;)c.key!==null?d.set(c.key,c):d.set(c.index,c),c=c.sibling;return d}function s(d,c){return d=gt(d,c),d.index=0,d.sibling=null,d}function i(d,c,f){return d.index=f,e?(f=d.alternate,f!==null?(f=f.index,f<c?(d.flags|=2,c):f):(d.flags|=2,c)):(d.flags|=1048576,c)}function o(d){return e&&d.alternate===null&&(d.flags|=2),d}function a(d,c,f,g){return c===null||c.tag!==6?(c=al(f,d.mode,g),c.return=d,c):(c=s(c,f),c.return=d,c)}function u(d,c,f,g){var j=f.type;return j===Bt?h(d,c,f.props.children,g,f.key):c!==null&&(c.elementType===j||typeof j=="object"&&j!==null&&j.$$typeof===rt&&Wo(j)===c.type)?(g=s(c,f.props),g.ref=Mn(d,c,f),g.return=d,g):(g=Wr(f.type,f.key,f.props,null,d.mode,g),g.ref=Mn(d,c,f),g.return=d,g)}function p(d,c,f,g){return c===null||c.tag!==4||c.stateNode.containerInfo!==f.containerInfo||c.stateNode.implementation!==f.implementation?(c=ul(f,d.mode,g),c.return=d,c):(c=s(c,f.children||[]),c.return=d,c)}function h(d,c,f,g,j){return c===null||c.tag!==7?(c=_t(f,d.mode,g,j),c.return=d,c):(c=s(c,f),c.return=d,c)}function m(d,c,f){if(typeof c=="string"&&c!==""||typeof c=="number")return c=al(""+c,d.mode,f),c.return=d,c;if(typeof c=="object"&&c!==null){switch(c.$$typeof){case xr:return f=Wr(c.type,c.key,c.props,null,d.mode,f),f.ref=Mn(d,null,c),f.return=d,f;case Ht:return c=ul(c,d.mode,f),c.return=d,c;case rt:var g=c._init;return m(d,g(c._payload),f)}if(Pn(c)||kn(c))return c=_t(c,d.mode,f,null),c.return=d,c;br(d,c)}return null}function x(d,c,f,g){var j=c!==null?c.key:null;if(typeof f=="string"&&f!==""||typeof f=="number")return j!==null?null:a(d,c,""+f,g);if(typeof f=="object"&&f!==null){switch(f.$$typeof){case xr:return f.key===j?u(d,c,f,g):null;case Ht:return f.key===j?p(d,c,f,g):null;case rt:return j=f._init,x(d,c,j(f._payload),g)}if(Pn(f)||kn(f))return j!==null?null:h(d,c,f,g,null);br(d,f)}return null}function y(d,c,f,g,j){if(typeof g=="string"&&g!==""||typeof g=="number")return d=d.get(f)||null,a(c,d,""+g,j);if(typeof g=="object"&&g!==null){switch(g.$$typeof){case xr:return d=d.get(g.key===null?f:g.key)||null,u(c,d,g,j);case Ht:return d=d.get(g.key===null?f:g.key)||null,p(c,d,g,j);case rt:var k=g._init;return y(d,c,f,k(g._payload),j)}if(Pn(g)||kn(g))return d=d.get(f)||null,h(c,d,g,j,null);br(c,g)}return null}function w(d,c,f,g){for(var j=null,k=null,L=c,b=c=0,$=null;L!==null&&b<f.length;b++){L.index>b?($=L,L=null):$=L.sibling;var A=x(d,L,f[b],g);if(A===null){L===null&&(L=$);break}e&&L&&A.alternate===null&&t(d,L),c=i(A,c,b),k===null?j=A:k.sibling=A,k=A,L=$}if(b===f.length)return n(d,L),H&&Et(d,b),j;if(L===null){for(;b<f.length;b++)L=m(d,f[b],g),L!==null&&(c=i(L,c,b),k===null?j=L:k.sibling=L,k=L);return H&&Et(d,b),j}for(L=r(d,L);b<f.length;b++)$=y(L,d,b,f[b],g),$!==null&&(e&&$.alternate!==null&&L.delete($.key===null?b:$.key),c=i($,c,b),k===null?j=$:k.sibling=$,k=$);return e&&L.forEach(function(Ae){return t(d,Ae)}),H&&Et(d,b),j}function v(d,c,f,g){var j=kn(f);if(typeof j!="function")throw Error(S(150));if(f=j.call(f),f==null)throw Error(S(151));for(var k=j=null,L=c,b=c=0,$=null,A=f.next();L!==null&&!A.done;b++,A=f.next()){L.index>b?($=L,L=null):$=L.sibling;var Ae=x(d,L,A.value,g);if(Ae===null){L===null&&(L=$);break}e&&L&&Ae.alternate===null&&t(d,L),c=i(Ae,c,b),k===null?j=Ae:k.sibling=Ae,k=Ae,L=$}if(A.done)return n(d,L),H&&Et(d,b),j;if(L===null){for(;!A.done;b++,A=f.next())A=m(d,A.value,g),A!==null&&(c=i(A,c,b),k===null?j=A:k.sibling=A,k=A);return H&&Et(d,b),j}for(L=r(d,L);!A.done;b++,A=f.next())A=y(L,d,b,A.value,g),A!==null&&(e&&A.alternate!==null&&L.delete(A.key===null?b:A.key),c=i(A,c,b),k===null?j=A:k.sibling=A,k=A);return e&&L.forEach(function(Nn){return t(d,Nn)}),H&&Et(d,b),j}function N(d,c,f,g){if(typeof f=="object"&&f!==null&&f.type===Bt&&f.key===null&&(f=f.props.children),typeof f=="object"&&f!==null){switch(f.$$typeof){case xr:e:{for(var j=f.key,k=c;k!==null;){if(k.key===j){if(j=f.type,j===Bt){if(k.tag===7){n(d,k.sibling),c=s(k,f.props.children),c.return=d,d=c;break e}}else if(k.elementType===j||typeof j=="object"&&j!==null&&j.$$typeof===rt&&Wo(j)===k.type){n(d,k.sibling),c=s(k,f.props),c.ref=Mn(d,k,f),c.return=d,d=c;break e}n(d,k);break}else t(d,k);k=k.sibling}f.type===Bt?(c=_t(f.props.children,d.mode,g,f.key),c.return=d,d=c):(g=Wr(f.type,f.key,f.props,null,d.mode,g),g.ref=Mn(d,c,f),g.return=d,d=g)}return o(d);case Ht:e:{for(k=f.key;c!==null;){if(c.key===k)if(c.tag===4&&c.stateNode.containerInfo===f.containerInfo&&c.stateNode.implementation===f.implementation){n(d,c.sibling),c=s(c,f.children||[]),c.return=d,d=c;break e}else{n(d,c);break}else t(d,c);c=c.sibling}c=ul(f,d.mode,g),c.return=d,d=c}return o(d);case rt:return k=f._init,N(d,c,k(f._payload),g)}if(Pn(f))return w(d,c,f,g);if(kn(f))return v(d,c,f,g);br(d,f)}return typeof f=="string"&&f!==""||typeof f=="number"?(f=""+f,c!==null&&c.tag===6?(n(d,c.sibling),c=s(c,f),c.return=d,d=c):(n(d,c),c=al(f,d.mode,g),c.return=d,d=c),o(d)):n(d,c)}return N}var dn=Ru(!0),Ou=Ru(!1),ls=jt(null),is=null,Jt=null,_i=null;function zi(){_i=Jt=is=null}function Ii(e){var t=ls.current;V(ls),e._currentValue=t}function Fl(e,t,n){for(;e!==null;){var r=e.alternate;if((e.childLanes&t)!==t?(e.childLanes|=t,r!==null&&(r.childLanes|=t)):r!==null&&(r.childLanes&t)!==t&&(r.childLanes|=t),e===n)break;e=e.return}}function ln(e,t){is=e,_i=Jt=null,e=e.dependencies,e!==null&&e.firstContext!==null&&(e.lanes&t&&(he=!0),e.firstContext=null)}function Te(e){var t=e._currentValue;if(_i!==e)if(e={context:e,memoizedValue:t,next:null},Jt===null){if(is===null)throw Error(S(308));Jt=e,is.dependencies={lanes:0,firstContext:e}}else Jt=Jt.next=e;return t}var Dt=null;function Ri(e){Dt===null?Dt=[e]:Dt.push(e)}function $u(e,t,n,r){var s=t.interleaved;return s===null?(n.next=n,Ri(t)):(n.next=s.next,s.next=n),t.interleaved=n,qe(e,r)}function qe(e,t){e.lanes|=t;var n=e.alternate;for(n!==null&&(n.lanes|=t),n=e,e=e.return;e!==null;)e.childLanes|=t,n=e.alternate,n!==null&&(n.childLanes|=t),n=e,e=e.return;return n.tag===3?n.stateNode:null}var st=!1;function Oi(e){e.updateQueue={baseState:e.memoizedState,firstBaseUpdate:null,lastBaseUpdate:null,shared:{pending:null,interleaved:null,lanes:0},effects:null}}function Uu(e,t){e=e.updateQueue,t.updateQueue===e&&(t.updateQueue={baseState:e.baseState,firstBaseUpdate:e.firstBaseUpdate,lastBaseUpdate:e.lastBaseUpdate,shared:e.shared,effects:e.effects})}function Ye(e,t){return{eventTime:e,lane:t,tag:0,payload:null,callback:null,next:null}}function pt(e,t,n){var r=e.updateQueue;if(r===null)return null;if(r=r.shared,z&2){var s=r.pending;return s===null?t.next=t:(t.next=s.next,s.next=t),r.pending=t,qe(e,n)}return s=r.interleaved,s===null?(t.next=t,Ri(r)):(t.next=s.next,s.next=t),r.interleaved=t,qe(e,n)}function $r(e,t,n){if(t=t.updateQueue,t!==null&&(t=t.shared,(n&4194240)!==0)){var r=t.lanes;r&=e.pendingLanes,n|=r,t.lanes=n,Si(e,n)}}function Qo(e,t){var n=e.updateQueue,r=e.alternate;if(r!==null&&(r=r.updateQueue,n===r)){var s=null,i=null;if(n=n.firstBaseUpdate,n!==null){do{var o={eventTime:n.eventTime,lane:n.lane,tag:n.tag,payload:n.payload,callback:n.callback,next:null};i===null?s=i=o:i=i.next=o,n=n.next}while(n!==null);i===null?s=i=t:i=i.next=t}else s=i=t;n={baseState:r.baseState,firstBaseUpdate:s,lastBaseUpdate:i,shared:r.shared,effects:r.effects},e.updateQueue=n;return}e=n.lastBaseUpdate,e===null?n.firstBaseUpdate=t:e.next=t,n.lastBaseUpdate=t}function os(e,t,n,r){var s=e.updateQueue;st=!1;var i=s.firstBaseUpdate,o=s.lastBaseUpdate,a=s.shared.pending;if(a!==null){s.shared.pending=null;var u=a,p=u.next;u.next=null,o===null?i=p:o.next=p,o=u;var h=e.alternate;h!==null&&(h=h.updateQueue,a=h.lastBaseUpdate,a!==o&&(a===null?h.firstBaseUpdate=p:a.next=p,h.lastBaseUpdate=u))}if(i!==null){var m=s.baseState;o=0,h=p=u=null,a=i;do{var x=a.lane,y=a.eventTime;if((r&x)===x){h!==null&&(h=h.next={eventTime:y,lane:0,tag:a.tag,payload:a.payload,callback:a.callback,next:null});e:{var w=e,v=a;switch(x=t,y=n,v.tag){case 1:if(w=v.payload,typeof w=="function"){m=w.call(y,m,x);break e}m=w;break e;case 3:w.flags=w.flags&-65537|128;case 0:if(w=v.payload,x=typeof w=="function"?w.call(y,m,x):w,x==null)break e;m=Q({},m,x);break e;case 2:st=!0}}a.callback!==null&&a.lane!==0&&(e.flags|=64,x=s.effects,x===null?s.effects=[a]:x.push(a))}else y={eventTime:y,lane:x,tag:a.tag,payload:a.payload,callback:a.callback,next:null},h===null?(p=h=y,u=m):h=h.next=y,o|=x;if(a=a.next,a===null){if(a=s.shared.pending,a===null)break;x=a,a=x.next,x.next=null,s.lastBaseUpdate=x,s.shared.pending=null}}while(1);if(h===null&&(u=m),s.baseState=u,s.firstBaseUpdate=p,s.lastBaseUpdate=h,t=s.shared.interleaved,t!==null){s=t;do o|=s.lane,s=s.next;while(s!==t)}else i===null&&(s.shared.lanes=0);Ot|=o,e.lanes=o,e.memoizedState=m}}function Ko(e,t,n){if(e=t.effects,t.effects=null,e!==null)for(t=0;t<e.length;t++){var r=e[t],s=r.callback;if(s!==null){if(r.callback=null,r=n,typeof s!="function")throw Error(S(191,s));s.call(r)}}}var pr={},Be=jt(pr),nr=jt(pr),rr=jt(pr);function At(e){if(e===pr)throw Error(S(174));return e}function $i(e,t){switch(U(rr,t),U(nr,e),U(Be,pr),e=t.nodeType,e){case 9:case 11:t=(t=t.documentElement)?t.namespaceURI:jl(null,"");break;default:e=e===8?t.parentNode:t,t=e.namespaceURI||null,e=e.tagName,t=jl(t,e)}V(Be),U(Be,t)}function fn(){V(Be),V(nr),V(rr)}function Fu(e){At(rr.current);var t=At(Be.current),n=jl(t,e.type);t!==n&&(U(nr,e),U(Be,n))}function Ui(e){nr.current===e&&(V(Be),V(nr))}var B=jt(0);function as(e){for(var t=e;t!==null;){if(t.tag===13){var n=t.memoizedState;if(n!==null&&(n=n.dehydrated,n===null||n.data==="$?"||n.data==="$!"))return t}else if(t.tag===19&&t.memoizedProps.revealOrder!==void 0){if(t.flags&128)return t}else if(t.child!==null){t.child.return=t,t=t.child;continue}if(t===e)break;for(;t.sibling===null;){if(t.return===null||t.return===e)return null;t=t.return}t.sibling.return=t.return,t=t.sibling}return null}var nl=[];function Fi(){for(var e=0;e<nl.length;e++)nl[e]._workInProgressVersionPrimary=null;nl.length=0}var Ur=tt.ReactCurrentDispatcher,rl=tt.ReactCurrentBatchConfig,Rt=0,W=null,Z=null,ee=null,us=!1,Fn=!1,sr=0,Vf=0;function ie(){throw Error(S(321))}function Vi(e,t){if(t===null)return!1;for(var n=0;n<t.length&&n<e.length;n++)if(!$e(e[n],t[n]))return!1;return!0}function Hi(e,t,n,r,s,i){if(Rt=i,W=t,t.memoizedState=null,t.updateQueue=null,t.lanes=0,Ur.current=e===null||e.memoizedState===null?Qf:Kf,e=n(r,s),Fn){i=0;do{if(Fn=!1,sr=0,25<=i)throw Error(S(301));i+=1,ee=Z=null,t.updateQueue=null,Ur.current=Gf,e=n(r,s)}while(Fn)}if(Ur.current=cs,t=Z!==null&&Z.next!==null,Rt=0,ee=Z=W=null,us=!1,t)throw Error(S(300));return e}function Bi(){var e=sr!==0;return sr=0,e}function Fe(){var e={memoizedState:null,baseState:null,baseQueue:null,queue:null,next:null};return ee===null?W.memoizedState=ee=e:ee=ee.next=e,ee}function De(){if(Z===null){var e=W.alternate;e=e!==null?e.memoizedState:null}else e=Z.next;var t=ee===null?W.memoizedState:ee.next;if(t!==null)ee=t,Z=e;else{if(e===null)throw Error(S(310));Z=e,e={memoizedState:Z.memoizedState,baseState:Z.baseState,baseQueue:Z.baseQueue,queue:Z.queue,next:null},ee===null?W.memoizedState=ee=e:ee=ee.next=e}return ee}function lr(e,t){return typeof t=="function"?t(e):t}function sl(e){var t=De(),n=t.queue;if(n===null)throw Error(S(311));n.lastRenderedReducer=e;var r=Z,s=r.baseQueue,i=n.pending;if(i!==null){if(s!==null){var o=s.next;s.next=i.next,i.next=o}r.baseQueue=s=i,n.pending=null}if(s!==null){i=s.next,r=r.baseState;var a=o=null,u=null,p=i;do{var h=p.lane;if((Rt&h)===h)u!==null&&(u=u.next={lane:0,action:p.action,hasEagerState:p.hasEagerState,eagerState:p.eagerState,next:null}),r=p.hasEagerState?p.eagerState:e(r,p.action);else{var m={lane:h,action:p.action,hasEagerState:p.hasEagerState,eagerState:p.eagerState,next:null};u===null?(a=u=m,o=r):u=u.next=m,W.lanes|=h,Ot|=h}p=p.next}while(p!==null&&p!==i);u===null?o=r:u.next=a,$e(r,t.memoizedState)||(he=!0),t.memoizedState=r,t.baseState=o,t.baseQueue=u,n.lastRenderedState=r}if(e=n.interleaved,e!==null){s=e;do i=s.lane,W.lanes|=i,Ot|=i,s=s.next;while(s!==e)}else s===null&&(n.lanes=0);return[t.memoizedState,n.dispatch]}function ll(e){var t=De(),n=t.queue;if(n===null)throw Error(S(311));n.lastRenderedReducer=e;var r=n.dispatch,s=n.pending,i=t.memoizedState;if(s!==null){n.pending=null;var o=s=s.next;do i=e(i,o.action),o=o.next;while(o!==s);$e(i,t.memoizedState)||(he=!0),t.memoizedState=i,t.baseQueue===null&&(t.baseState=i),n.lastRenderedState=i}return[i,r]}function Vu(){}function Hu(e,t){var n=W,r=De(),s=t(),i=!$e(r.memoizedState,s);if(i&&(r.memoizedState=s,he=!0),r=r.queue,Wi(Qu.bind(null,n,r,e),[e]),r.getSnapshot!==t||i||ee!==null&&ee.memoizedState.tag&1){if(n.flags|=2048,ir(9,Wu.bind(null,n,r,s,t),void 0,null),te===null)throw Error(S(349));Rt&30||Bu(n,t,s)}return s}function Bu(e,t,n){e.flags|=16384,e={getSnapshot:t,value:n},t=W.updateQueue,t===null?(t={lastEffect:null,stores:null},W.updateQueue=t,t.stores=[e]):(n=t.stores,n===null?t.stores=[e]:n.push(e))}function Wu(e,t,n,r){t.value=n,t.getSnapshot=r,Ku(t)&&Gu(e)}function Qu(e,t,n){return n(function(){Ku(t)&&Gu(e)})}function Ku(e){var t=e.getSnapshot;e=e.value;try{var n=t();return!$e(e,n)}catch{return!0}}function Gu(e){var t=qe(e,1);t!==null&&Oe(t,e,1,-1)}function Go(e){var t=Fe();return typeof e=="function"&&(e=e()),t.memoizedState=t.baseState=e,e={pending:null,interleaved:null,lanes:0,dispatch:null,lastRenderedReducer:lr,lastRenderedState:e},t.queue=e,e=e.dispatch=Wf.bind(null,W,e),[t.memoizedState,e]}function ir(e,t,n,r){return e={tag:e,create:t,destroy:n,deps:r,next:null},t=W.updateQueue,t===null?(t={lastEffect:null,stores:null},W.updateQueue=t,t.lastEffect=e.next=e):(n=t.lastEffect,n===null?t.lastEffect=e.next=e:(r=n.next,n.next=e,e.next=r,t.lastEffect=e)),e}function Yu(){return De().memoizedState}function Fr(e,t,n,r){var s=Fe();W.flags|=e,s.memoizedState=ir(1|t,n,void 0,r===void 0?null:r)}function Es(e,t,n,r){var s=De();r=r===void 0?null:r;var i=void 0;if(Z!==null){var o=Z.memoizedState;if(i=o.destroy,r!==null&&Vi(r,o.deps)){s.memoizedState=ir(t,n,i,r);return}}W.flags|=e,s.memoizedState=ir(1|t,n,i,r)}function Yo(e,t){return Fr(8390656,8,e,t)}function Wi(e,t){return Es(2048,8,e,t)}function Xu(e,t){return Es(4,2,e,t)}function Zu(e,t){return Es(4,4,e,t)}function Ju(e,t){if(typeof t=="function")return e=e(),t(e),function(){t(null)};if(t!=null)return e=e(),t.current=e,function(){t.current=null}}function qu(e,t,n){return n=n!=null?n.concat([e]):null,Es(4,4,Ju.bind(null,t,e),n)}function Qi(){}function ec(e,t){var n=De();t=t===void 0?null:t;var r=n.memoizedState;return r!==null&&t!==null&&Vi(t,r[1])?r[0]:(n.memoizedState=[e,t],e)}function tc(e,t){var n=De();t=t===void 0?null:t;var r=n.memoizedState;return r!==null&&t!==null&&Vi(t,r[1])?r[0]:(e=e(),n.memoizedState=[e,t],e)}function nc(e,t,n){return Rt&21?($e(n,t)||(n=ou(),W.lanes|=n,Ot|=n,e.baseState=!0),t):(e.baseState&&(e.baseState=!1,he=!0),e.memoizedState=n)}function Hf(e,t){var n=R;R=n!==0&&4>n?n:4,e(!0);var r=rl.transition;rl.transition={};try{e(!1),t()}finally{R=n,rl.transition=r}}function rc(){return De().memoizedState}function Bf(e,t,n){var r=ht(e);if(n={lane:r,action:n,hasEagerState:!1,eagerState:null,next:null},sc(e))lc(t,n);else if(n=$u(e,t,n,r),n!==null){var s=de();Oe(n,e,r,s),ic(n,t,r)}}function Wf(e,t,n){var r=ht(e),s={lane:r,action:n,hasEagerState:!1,eagerState:null,next:null};if(sc(e))lc(t,s);else{var i=e.alternate;if(e.lanes===0&&(i===null||i.lanes===0)&&(i=t.lastRenderedReducer,i!==null))try{var o=t.lastRenderedState,a=i(o,n);if(s.hasEagerState=!0,s.eagerState=a,$e(a,o)){var u=t.interleaved;u===null?(s.next=s,Ri(t)):(s.next=u.next,u.next=s),t.interleaved=s;return}}catch{}finally{}n=$u(e,t,s,r),n!==null&&(s=de(),Oe(n,e,r,s),ic(n,t,r))}}function sc(e){var t=e.alternate;return e===W||t!==null&&t===W}function lc(e,t){Fn=us=!0;var n=e.pending;n===null?t.next=t:(t.next=n.next,n.next=t),e.pending=t}function ic(e,t,n){if(n&4194240){var r=t.lanes;r&=e.pendingLanes,n|=r,t.lanes=n,Si(e,n)}}var cs={readContext:Te,useCallback:ie,useContext:ie,useEffect:ie,useImperativeHandle:ie,useInsertionEffect:ie,useLayoutEffect:ie,useMemo:ie,useReducer:ie,useRef:ie,useState:ie,useDebugValue:ie,useDeferredValue:ie,useTransition:ie,useMutableSource:ie,useSyncExternalStore:ie,useId:ie,unstable_isNewReconciler:!1},Qf={readContext:Te,useCallback:function(e,t){return Fe().memoizedState=[e,t===void 0?null:t],e},useContext:Te,useEffect:Yo,useImperativeHandle:function(e,t,n){return n=n!=null?n.concat([e]):null,Fr(4194308,4,Ju.bind(null,t,e),n)},useLayoutEffect:function(e,t){return Fr(4194308,4,e,t)},useInsertionEffect:function(e,t){return Fr(4,2,e,t)},useMemo:function(e,t){var n=Fe();return t=t===void 0?null:t,e=e(),n.memoizedState=[e,t],e},useReducer:function(e,t,n){var r=Fe();return t=n!==void 0?n(t):t,r.memoizedState=r.baseState=t,e={pending:null,interleaved:null,lanes:0,dispatch:null,lastRenderedReducer:e,lastRenderedState:t},r.queue=e,e=e.dispatch=Bf.bind(null,W,e),[r.memoizedState,e]},useRef:function(e){var t=Fe();return e={current:e},t.memoizedState=e},useState:Go,useDebugValue:Qi,useDeferredValue:function(e){return Fe().memoizedState=e},useTransition:function(){var e=Go(!1),t=e[0];return e=Hf.bind(null,e[1]),Fe().memoizedState=e,[t,e]},useMutableSource:function(){},useSyncExternalStore:function(e,t,n){var r=W,s=Fe();if(H){if(n===void 0)throw Error(S(407));n=n()}else{if(n=t(),te===null)throw Error(S(349));Rt&30||Bu(r,t,n)}s.memoizedState=n;var i={value:n,getSnapshot:t};return s.queue=i,Yo(Qu.bind(null,r,i,e),[e]),r.flags|=2048,ir(9,Wu.bind(null,r,i,n,t),void 0,null),n},useId:function(){var e=Fe(),t=te.identifierPrefix;if(H){var n=Ge,r=Ke;n=(r&~(1<<32-Re(r)-1)).toString(32)+n,t=":"+t+"R"+n,n=sr++,0<n&&(t+="H"+n.toString(32)),t+=":"}else n=Vf++,t=":"+t+"r"+n.toString(32)+":";return e.memoizedState=t},unstable_isNewReconciler:!1},Kf={readContext:Te,useCallback:ec,useContext:Te,useEffect:Wi,useImperativeHandle:qu,useInsertionEffect:Xu,useLayoutEffect:Zu,useMemo:tc,useReducer:sl,useRef:Yu,useState:function(){return sl(lr)},useDebugValue:Qi,useDeferredValue:function(e){var t=De();return nc(t,Z.memoizedState,e)},useTransition:function(){var e=sl(lr)[0],t=De().memoizedState;return[e,t]},useMutableSource:Vu,useSyncExternalStore:Hu,useId:rc,unstable_isNewReconciler:!1},Gf={readContext:Te,useCallback:ec,useContext:Te,useEffect:Wi,useImperativeHandle:qu,useInsertionEffect:Xu,useLayoutEffect:Zu,useMemo:tc,useReducer:ll,useRef:Yu,useState:function(){return ll(lr)},useDebugValue:Qi,useDeferredValue:function(e){var t=De();return Z===null?t.memoizedState=e:nc(t,Z.memoizedState,e)},useTransition:function(){var e=ll(lr)[0],t=De().memoizedState;return[e,t]},useMutableSource:Vu,useSyncExternalStore:Hu,useId:rc,unstable_isNewReconciler:!1};function _e(e,t){if(e&&e.defaultProps){t=Q({},t),e=e.defaultProps;for(var n in e)t[n]===void 0&&(t[n]=e[n]);return t}return t}function Vl(e,t,n,r){t=e.memoizedState,n=n(r,t),n=n==null?t:Q({},t,n),e.memoizedState=n,e.lanes===0&&(e.updateQueue.baseState=n)}var Ls={isMounted:function(e){return(e=e._reactInternals)?Ft(e)===e:!1},enqueueSetState:function(e,t,n){e=e._reactInternals;var r=de(),s=ht(e),i=Ye(r,s);i.payload=t,n!=null&&(i.callback=n),t=pt(e,i,s),t!==null&&(Oe(t,e,s,r),$r(t,e,s))},enqueueReplaceState:function(e,t,n){e=e._reactInternals;var r=de(),s=ht(e),i=Ye(r,s);i.tag=1,i.payload=t,n!=null&&(i.callback=n),t=pt(e,i,s),t!==null&&(Oe(t,e,s,r),$r(t,e,s))},enqueueForceUpdate:function(e,t){e=e._reactInternals;var n=de(),r=ht(e),s=Ye(n,r);s.tag=2,t!=null&&(s.callback=t),t=pt(e,s,r),t!==null&&(Oe(t,e,r,n),$r(t,e,r))}};function Xo(e,t,n,r,s,i,o){return e=e.stateNode,typeof e.shouldComponentUpdate=="function"?e.shouldComponentUpdate(r,i,o):t.prototype&&t.prototype.isPureReactComponent?!Jn(n,r)||!Jn(s,i):!0}function oc(e,t,n){var r=!1,s=vt,i=t.contextType;return typeof i=="object"&&i!==null?i=Te(i):(s=xe(t)?zt:ue.current,r=t.contextTypes,i=(r=r!=null)?un(e,s):vt),t=new t(n,i),e.memoizedState=t.state!==null&&t.state!==void 0?t.state:null,t.updater=Ls,e.stateNode=t,t._reactInternals=e,r&&(e=e.stateNode,e.__reactInternalMemoizedUnmaskedChildContext=s,e.__reactInternalMemoizedMaskedChildContext=i),t}function Zo(e,t,n,r){e=t.state,typeof t.componentWillReceiveProps=="function"&&t.componentWillReceiveProps(n,r),typeof t.UNSAFE_componentWillReceiveProps=="function"&&t.UNSAFE_componentWillReceiveProps(n,r),t.state!==e&&Ls.enqueueReplaceState(t,t.state,null)}function Hl(e,t,n,r){var s=e.stateNode;s.props=n,s.state=e.memoizedState,s.refs={},Oi(e);var i=t.contextType;typeof i=="object"&&i!==null?s.context=Te(i):(i=xe(t)?zt:ue.current,s.context=un(e,i)),s.state=e.memoizedState,i=t.getDerivedStateFromProps,typeof i=="function"&&(Vl(e,t,i,n),s.state=e.memoizedState),typeof t.getDerivedStateFromProps=="function"||typeof s.getSnapshotBeforeUpdate=="function"||typeof s.UNSAFE_componentWillMount!="function"&&typeof s.componentWillMount!="function"||(t=s.state,typeof s.componentWillMount=="function"&&s.componentWillMount(),typeof s.UNSAFE_componentWillMount=="function"&&s.UNSAFE_componentWillMount(),t!==s.state&&Ls.enqueueReplaceState(s,s.state,null),os(e,n,s,r),s.state=e.memoizedState),typeof s.componentDidMount=="function"&&(e.flags|=4194308)}function pn(e,t){try{var n="",r=t;do n+=jd(r),r=r.return;while(r);var s=n}catch(i){s=`
Error generating stack: `+i.message+`
`+i.stack}return{value:e,source:t,stack:s,digest:null}}function il(e,t,n){return{value:e,source:null,stack:n??null,digest:t??null}}function Bl(e,t){try{console.error(t.value)}catch(n){setTimeout(function(){throw n})}}var Yf=typeof WeakMap=="function"?WeakMap:Map;function ac(e,t,n){n=Ye(-1,n),n.tag=3,n.payload={element:null};var r=t.value;return n.callback=function(){fs||(fs=!0,ei=r),Bl(e,t)},n}function uc(e,t,n){n=Ye(-1,n),n.tag=3;var r=e.type.getDerivedStateFromError;if(typeof r=="function"){var s=t.value;n.payload=function(){return r(s)},n.callback=function(){Bl(e,t)}}var i=e.stateNode;return i!==null&&typeof i.componentDidCatch=="function"&&(n.callback=function(){Bl(e,t),typeof r!="function"&&(mt===null?mt=new Set([this]):mt.add(this));var o=t.stack;this.componentDidCatch(t.value,{componentStack:o!==null?o:""})}),n}function Jo(e,t,n){var r=e.pingCache;if(r===null){r=e.pingCache=new Yf;var s=new Set;r.set(t,s)}else s=r.get(t),s===void 0&&(s=new Set,r.set(t,s));s.has(n)||(s.add(n),e=up.bind(null,e,t,n),t.then(e,e))}function qo(e){do{var t;if((t=e.tag===13)&&(t=e.memoizedState,t=t!==null?t.dehydrated!==null:!0),t)return e;e=e.return}while(e!==null);return null}function ea(e,t,n,r,s){return e.mode&1?(e.flags|=65536,e.lanes=s,e):(e===t?e.flags|=65536:(e.flags|=128,n.flags|=131072,n.flags&=-52805,n.tag===1&&(n.alternate===null?n.tag=17:(t=Ye(-1,1),t.tag=2,pt(n,t,1))),n.lanes|=1),e)}var Xf=tt.ReactCurrentOwner,he=!1;function ce(e,t,n,r){t.child=e===null?Ou(t,null,n,r):dn(t,e.child,n,r)}function ta(e,t,n,r,s){n=n.render;var i=t.ref;return ln(t,s),r=Hi(e,t,n,r,i,s),n=Bi(),e!==null&&!he?(t.updateQueue=e.updateQueue,t.flags&=-2053,e.lanes&=~s,et(e,t,s)):(H&&n&&Di(t),t.flags|=1,ce(e,t,r,s),t.child)}function na(e,t,n,r,s){if(e===null){var i=n.type;return typeof i=="function"&&!eo(i)&&i.defaultProps===void 0&&n.compare===null&&n.defaultProps===void 0?(t.tag=15,t.type=i,cc(e,t,i,r,s)):(e=Wr(n.type,null,r,t,t.mode,s),e.ref=t.ref,e.return=t,t.child=e)}if(i=e.child,!(e.lanes&s)){var o=i.memoizedProps;if(n=n.compare,n=n!==null?n:Jn,n(o,r)&&e.ref===t.ref)return et(e,t,s)}return t.flags|=1,e=gt(i,r),e.ref=t.ref,e.return=t,t.child=e}function cc(e,t,n,r,s){if(e!==null){var i=e.memoizedProps;if(Jn(i,r)&&e.ref===t.ref)if(he=!1,t.pendingProps=r=i,(e.lanes&s)!==0)e.flags&131072&&(he=!0);else return t.lanes=e.lanes,et(e,t,s)}return Wl(e,t,n,r,s)}function dc(e,t,n){var r=t.pendingProps,s=r.children,i=e!==null?e.memoizedState:null;if(r.mode==="hidden")if(!(t.mode&1))t.memoizedState={baseLanes:0,cachePool:null,transitions:null},U(en,ve),ve|=n;else{if(!(n&1073741824))return e=i!==null?i.baseLanes|n:n,t.lanes=t.childLanes=1073741824,t.memoizedState={baseLanes:e,cachePool:null,transitions:null},t.updateQueue=null,U(en,ve),ve|=e,null;t.memoizedState={baseLanes:0,cachePool:null,transitions:null},r=i!==null?i.baseLanes:n,U(en,ve),ve|=r}else i!==null?(r=i.baseLanes|n,t.memoizedState=null):r=n,U(en,ve),ve|=r;return ce(e,t,s,n),t.child}function fc(e,t){var n=t.ref;(e===null&&n!==null||e!==null&&e.ref!==n)&&(t.flags|=512,t.flags|=2097152)}function Wl(e,t,n,r,s){var i=xe(n)?zt:ue.current;return i=un(t,i),ln(t,s),n=Hi(e,t,n,r,i,s),r=Bi(),e!==null&&!he?(t.updateQueue=e.updateQueue,t.flags&=-2053,e.lanes&=~s,et(e,t,s)):(H&&r&&Di(t),t.flags|=1,ce(e,t,n,s),t.child)}function ra(e,t,n,r,s){if(xe(n)){var i=!0;ns(t)}else i=!1;if(ln(t,s),t.stateNode===null)Vr(e,t),oc(t,n,r),Hl(t,n,r,s),r=!0;else if(e===null){var o=t.stateNode,a=t.memoizedProps;o.props=a;var u=o.context,p=n.contextType;typeof p=="object"&&p!==null?p=Te(p):(p=xe(n)?zt:ue.current,p=un(t,p));var h=n.getDerivedStateFromProps,m=typeof h=="function"||typeof o.getSnapshotBeforeUpdate=="function";m||typeof o.UNSAFE_componentWillReceiveProps!="function"&&typeof o.componentWillReceiveProps!="function"||(a!==r||u!==p)&&Zo(t,o,r,p),st=!1;var x=t.memoizedState;o.state=x,os(t,r,o,s),u=t.memoizedState,a!==r||x!==u||ge.current||st?(typeof h=="function"&&(Vl(t,n,h,r),u=t.memoizedState),(a=st||Xo(t,n,a,r,x,u,p))?(m||typeof o.UNSAFE_componentWillMount!="function"&&typeof o.componentWillMount!="function"||(typeof o.componentWillMount=="function"&&o.componentWillMount(),typeof o.UNSAFE_componentWillMount=="function"&&o.UNSAFE_componentWillMount()),typeof o.componentDidMount=="function"&&(t.flags|=4194308)):(typeof o.componentDidMount=="function"&&(t.flags|=4194308),t.memoizedProps=r,t.memoizedState=u),o.props=r,o.state=u,o.context=p,r=a):(typeof o.componentDidMount=="function"&&(t.flags|=4194308),r=!1)}else{o=t.stateNode,Uu(e,t),a=t.memoizedProps,p=t.type===t.elementType?a:_e(t.type,a),o.props=p,m=t.pendingProps,x=o.context,u=n.contextType,typeof u=="object"&&u!==null?u=Te(u):(u=xe(n)?zt:ue.current,u=un(t,u));var y=n.getDerivedStateFromProps;(h=typeof y=="function"||typeof o.getSnapshotBeforeUpdate=="function")||typeof o.UNSAFE_componentWillReceiveProps!="function"&&typeof o.componentWillReceiveProps!="function"||(a!==m||x!==u)&&Zo(t,o,r,u),st=!1,x=t.memoizedState,o.state=x,os(t,r,o,s);var w=t.memoizedState;a!==m||x!==w||ge.current||st?(typeof y=="function"&&(Vl(t,n,y,r),w=t.memoizedState),(p=st||Xo(t,n,p,r,x,w,u)||!1)?(h||typeof o.UNSAFE_componentWillUpdate!="function"&&typeof o.componentWillUpdate!="function"||(typeof o.componentWillUpdate=="function"&&o.componentWillUpdate(r,w,u),typeof o.UNSAFE_componentWillUpdate=="function"&&o.UNSAFE_componentWillUpdate(r,w,u)),typeof o.componentDidUpdate=="function"&&(t.flags|=4),typeof o.getSnapshotBeforeUpdate=="function"&&(t.flags|=1024)):(typeof o.componentDidUpdate!="function"||a===e.memoizedProps&&x===e.memoizedState||(t.flags|=4),typeof o.getSnapshotBeforeUpdate!="function"||a===e.memoizedProps&&x===e.memoizedState||(t.flags|=1024),t.memoizedProps=r,t.memoizedState=w),o.props=r,o.state=w,o.context=u,r=p):(typeof o.componentDidUpdate!="function"||a===e.memoizedProps&&x===e.memoizedState||(t.flags|=4),typeof o.getSnapshotBeforeUpdate!="function"||a===e.memoizedProps&&x===e.memoizedState||(t.flags|=1024),r=!1)}return Ql(e,t,n,r,i,s)}function Ql(e,t,n,r,s,i){fc(e,t);var o=(t.flags&128)!==0;if(!r&&!o)return s&&Vo(t,n,!1),et(e,t,i);r=t.stateNode,Xf.current=t;var a=o&&typeof n.getDerivedStateFromError!="function"?null:r.render();return t.flags|=1,e!==null&&o?(t.child=dn(t,e.child,null,i),t.child=dn(t,null,a,i)):ce(e,t,a,i),t.memoizedState=r.state,s&&Vo(t,n,!0),t.child}function pc(e){var t=e.stateNode;t.pendingContext?Fo(e,t.pendingContext,t.pendingContext!==t.context):t.context&&Fo(e,t.context,!1),$i(e,t.containerInfo)}function sa(e,t,n,r,s){return cn(),Pi(s),t.flags|=256,ce(e,t,n,r),t.child}var Kl={dehydrated:null,treeContext:null,retryLane:0};function Gl(e){return{baseLanes:e,cachePool:null,transitions:null}}function mc(e,t,n){var r=t.pendingProps,s=B.current,i=!1,o=(t.flags&128)!==0,a;if((a=o)||(a=e!==null&&e.memoizedState===null?!1:(s&2)!==0),a?(i=!0,t.flags&=-129):(e===null||e.memoizedState!==null)&&(s|=1),U(B,s&1),e===null)return Ul(t),e=t.memoizedState,e!==null&&(e=e.dehydrated,e!==null)?(t.mode&1?e.data==="$!"?t.lanes=8:t.lanes=1073741824:t.lanes=1,null):(o=r.children,e=r.fallback,i?(r=t.mode,i=t.child,o={mode:"hidden",children:o},!(r&1)&&i!==null?(i.childLanes=0,i.pendingProps=o):i=Ts(o,r,0,null),e=_t(e,r,n,null),i.return=t,e.return=t,i.sibling=e,t.child=i,t.child.memoizedState=Gl(n),t.memoizedState=Kl,e):Ki(t,o));if(s=e.memoizedState,s!==null&&(a=s.dehydrated,a!==null))return Zf(e,t,o,r,a,s,n);if(i){i=r.fallback,o=t.mode,s=e.child,a=s.sibling;var u={mode:"hidden",children:r.children};return!(o&1)&&t.child!==s?(r=t.child,r.childLanes=0,r.pendingProps=u,t.deletions=null):(r=gt(s,u),r.subtreeFlags=s.subtreeFlags&14680064),a!==null?i=gt(a,i):(i=_t(i,o,n,null),i.flags|=2),i.return=t,r.return=t,r.sibling=i,t.child=r,r=i,i=t.child,o=e.child.memoizedState,o=o===null?Gl(n):{baseLanes:o.baseLanes|n,cachePool:null,transitions:o.transitions},i.memoizedState=o,i.childLanes=e.childLanes&~n,t.memoizedState=Kl,r}return i=e.child,e=i.sibling,r=gt(i,{mode:"visible",children:r.children}),!(t.mode&1)&&(r.lanes=n),r.return=t,r.sibling=null,e!==null&&(n=t.deletions,n===null?(t.deletions=[e],t.flags|=16):n.push(e)),t.child=r,t.memoizedState=null,r}function Ki(e,t){return t=Ts({mode:"visible",children:t},e.mode,0,null),t.return=e,e.child=t}function Mr(e,t,n,r){return r!==null&&Pi(r),dn(t,e.child,null,n),e=Ki(t,t.pendingProps.children),e.flags|=2,t.memoizedState=null,e}function Zf(e,t,n,r,s,i,o){if(n)return t.flags&256?(t.flags&=-257,r=il(Error(S(422))),Mr(e,t,o,r)):t.memoizedState!==null?(t.child=e.child,t.flags|=128,null):(i=r.fallback,s=t.mode,r=Ts({mode:"visible",children:r.children},s,0,null),i=_t(i,s,o,null),i.flags|=2,r.return=t,i.return=t,r.sibling=i,t.child=r,t.mode&1&&dn(t,e.child,null,o),t.child.memoizedState=Gl(o),t.memoizedState=Kl,i);if(!(t.mode&1))return Mr(e,t,o,null);if(s.data==="$!"){if(r=s.nextSibling&&s.nextSibling.dataset,r)var a=r.dgst;return r=a,i=Error(S(419)),r=il(i,r,void 0),Mr(e,t,o,r)}if(a=(o&e.childLanes)!==0,he||a){if(r=te,r!==null){switch(o&-o){case 4:s=2;break;case 16:s=8;break;case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:s=32;break;case 536870912:s=268435456;break;default:s=0}s=s&(r.suspendedLanes|o)?0:s,s!==0&&s!==i.retryLane&&(i.retryLane=s,qe(e,s),Oe(r,e,s,-1))}return qi(),r=il(Error(S(421))),Mr(e,t,o,r)}return s.data==="$?"?(t.flags|=128,t.child=e.child,t=cp.bind(null,e),s._reactRetry=t,null):(e=i.treeContext,we=ft(s.nextSibling),je=t,H=!0,Ie=null,e!==null&&(Ee[Le++]=Ke,Ee[Le++]=Ge,Ee[Le++]=It,Ke=e.id,Ge=e.overflow,It=t),t=Ki(t,r.children),t.flags|=4096,t)}function la(e,t,n){e.lanes|=t;var r=e.alternate;r!==null&&(r.lanes|=t),Fl(e.return,t,n)}function ol(e,t,n,r,s){var i=e.memoizedState;i===null?e.memoizedState={isBackwards:t,rendering:null,renderingStartTime:0,last:r,tail:n,tailMode:s}:(i.isBackwards=t,i.rendering=null,i.renderingStartTime=0,i.last=r,i.tail=n,i.tailMode=s)}function hc(e,t,n){var r=t.pendingProps,s=r.revealOrder,i=r.tail;if(ce(e,t,r.children,n),r=B.current,r&2)r=r&1|2,t.flags|=128;else{if(e!==null&&e.flags&128)e:for(e=t.child;e!==null;){if(e.tag===13)e.memoizedState!==null&&la(e,n,t);else if(e.tag===19)la(e,n,t);else if(e.child!==null){e.child.return=e,e=e.child;continue}if(e===t)break e;for(;e.sibling===null;){if(e.return===null||e.return===t)break e;e=e.return}e.sibling.return=e.return,e=e.sibling}r&=1}if(U(B,r),!(t.mode&1))t.memoizedState=null;else switch(s){case"forwards":for(n=t.child,s=null;n!==null;)e=n.alternate,e!==null&&as(e)===null&&(s=n),n=n.sibling;n=s,n===null?(s=t.child,t.child=null):(s=n.sibling,n.sibling=null),ol(t,!1,s,n,i);break;case"backwards":for(n=null,s=t.child,t.child=null;s!==null;){if(e=s.alternate,e!==null&&as(e)===null){t.child=s;break}e=s.sibling,s.sibling=n,n=s,s=e}ol(t,!0,n,null,i);break;case"together":ol(t,!1,null,null,void 0);break;default:t.memoizedState=null}return t.child}function Vr(e,t){!(t.mode&1)&&e!==null&&(e.alternate=null,t.alternate=null,t.flags|=2)}function et(e,t,n){if(e!==null&&(t.dependencies=e.dependencies),Ot|=t.lanes,!(n&t.childLanes))return null;if(e!==null&&t.child!==e.child)throw Error(S(153));if(t.child!==null){for(e=t.child,n=gt(e,e.pendingProps),t.child=n,n.return=t;e.sibling!==null;)e=e.sibling,n=n.sibling=gt(e,e.pendingProps),n.return=t;n.sibling=null}return t.child}function Jf(e,t,n){switch(t.tag){case 3:pc(t),cn();break;case 5:Fu(t);break;case 1:xe(t.type)&&ns(t);break;case 4:$i(t,t.stateNode.containerInfo);break;case 10:var r=t.type._context,s=t.memoizedProps.value;U(ls,r._currentValue),r._currentValue=s;break;case 13:if(r=t.memoizedState,r!==null)return r.dehydrated!==null?(U(B,B.current&1),t.flags|=128,null):n&t.child.childLanes?mc(e,t,n):(U(B,B.current&1),e=et(e,t,n),e!==null?e.sibling:null);U(B,B.current&1);break;case 19:if(r=(n&t.childLanes)!==0,e.flags&128){if(r)return hc(e,t,n);t.flags|=128}if(s=t.memoizedState,s!==null&&(s.rendering=null,s.tail=null,s.lastEffect=null),U(B,B.current),r)break;return null;case 22:case 23:return t.lanes=0,dc(e,t,n)}return et(e,t,n)}var gc,Yl,xc,yc;gc=function(e,t){for(var n=t.child;n!==null;){if(n.tag===5||n.tag===6)e.appendChild(n.stateNode);else if(n.tag!==4&&n.child!==null){n.child.return=n,n=n.child;continue}if(n===t)break;for(;n.sibling===null;){if(n.return===null||n.return===t)return;n=n.return}n.sibling.return=n.return,n=n.sibling}};Yl=function(){};xc=function(e,t,n,r){var s=e.memoizedProps;if(s!==r){e=t.stateNode,At(Be.current);var i=null;switch(n){case"input":s=xl(e,s),r=xl(e,r),i=[];break;case"select":s=Q({},s,{value:void 0}),r=Q({},r,{value:void 0}),i=[];break;case"textarea":s=wl(e,s),r=wl(e,r),i=[];break;default:typeof s.onClick!="function"&&typeof r.onClick=="function"&&(e.onclick=es)}Nl(n,r);var o;n=null;for(p in s)if(!r.hasOwnProperty(p)&&s.hasOwnProperty(p)&&s[p]!=null)if(p==="style"){var a=s[p];for(o in a)a.hasOwnProperty(o)&&(n||(n={}),n[o]="")}else p!=="dangerouslySetInnerHTML"&&p!=="children"&&p!=="suppressContentEditableWarning"&&p!=="suppressHydrationWarning"&&p!=="autoFocus"&&(Wn.hasOwnProperty(p)?i||(i=[]):(i=i||[]).push(p,null));for(p in r){var u=r[p];if(a=s!=null?s[p]:void 0,r.hasOwnProperty(p)&&u!==a&&(u!=null||a!=null))if(p==="style")if(a){for(o in a)!a.hasOwnProperty(o)||u&&u.hasOwnProperty(o)||(n||(n={}),n[o]="");for(o in u)u.hasOwnProperty(o)&&a[o]!==u[o]&&(n||(n={}),n[o]=u[o])}else n||(i||(i=[]),i.push(p,n)),n=u;else p==="dangerouslySetInnerHTML"?(u=u?u.__html:void 0,a=a?a.__html:void 0,u!=null&&a!==u&&(i=i||[]).push(p,u)):p==="children"?typeof u!="string"&&typeof u!="number"||(i=i||[]).push(p,""+u):p!=="suppressContentEditableWarning"&&p!=="suppressHydrationWarning"&&(Wn.hasOwnProperty(p)?(u!=null&&p==="onScroll"&&F("scroll",e),i||a===u||(i=[])):(i=i||[]).push(p,u))}n&&(i=i||[]).push("style",n);var p=i;(t.updateQueue=p)&&(t.flags|=4)}};yc=function(e,t,n,r){n!==r&&(t.flags|=4)};function Tn(e,t){if(!H)switch(e.tailMode){case"hidden":t=e.tail;for(var n=null;t!==null;)t.alternate!==null&&(n=t),t=t.sibling;n===null?e.tail=null:n.sibling=null;break;case"collapsed":n=e.tail;for(var r=null;n!==null;)n.alternate!==null&&(r=n),n=n.sibling;r===null?t||e.tail===null?e.tail=null:e.tail.sibling=null:r.sibling=null}}function oe(e){var t=e.alternate!==null&&e.alternate.child===e.child,n=0,r=0;if(t)for(var s=e.child;s!==null;)n|=s.lanes|s.childLanes,r|=s.subtreeFlags&14680064,r|=s.flags&14680064,s.return=e,s=s.sibling;else for(s=e.child;s!==null;)n|=s.lanes|s.childLanes,r|=s.subtreeFlags,r|=s.flags,s.return=e,s=s.sibling;return e.subtreeFlags|=r,e.childLanes=n,t}function qf(e,t,n){var r=t.pendingProps;switch(Ai(t),t.tag){case 2:case 16:case 15:case 0:case 11:case 7:case 8:case 12:case 9:case 14:return oe(t),null;case 1:return xe(t.type)&&ts(),oe(t),null;case 3:return r=t.stateNode,fn(),V(ge),V(ue),Fi(),r.pendingContext&&(r.context=r.pendingContext,r.pendingContext=null),(e===null||e.child===null)&&(Lr(t)?t.flags|=4:e===null||e.memoizedState.isDehydrated&&!(t.flags&256)||(t.flags|=1024,Ie!==null&&(ri(Ie),Ie=null))),Yl(e,t),oe(t),null;case 5:Ui(t);var s=At(rr.current);if(n=t.type,e!==null&&t.stateNode!=null)xc(e,t,n,r,s),e.ref!==t.ref&&(t.flags|=512,t.flags|=2097152);else{if(!r){if(t.stateNode===null)throw Error(S(166));return oe(t),null}if(e=At(Be.current),Lr(t)){r=t.stateNode,n=t.type;var i=t.memoizedProps;switch(r[Ve]=t,r[tr]=i,e=(t.mode&1)!==0,n){case"dialog":F("cancel",r),F("close",r);break;case"iframe":case"object":case"embed":F("load",r);break;case"video":case"audio":for(s=0;s<zn.length;s++)F(zn[s],r);break;case"source":F("error",r);break;case"img":case"image":case"link":F("error",r),F("load",r);break;case"details":F("toggle",r);break;case"input":mo(r,i),F("invalid",r);break;case"select":r._wrapperState={wasMultiple:!!i.multiple},F("invalid",r);break;case"textarea":go(r,i),F("invalid",r)}Nl(n,i),s=null;for(var o in i)if(i.hasOwnProperty(o)){var a=i[o];o==="children"?typeof a=="string"?r.textContent!==a&&(i.suppressHydrationWarning!==!0&&Er(r.textContent,a,e),s=["children",a]):typeof a=="number"&&r.textContent!==""+a&&(i.suppressHydrationWarning!==!0&&Er(r.textContent,a,e),s=["children",""+a]):Wn.hasOwnProperty(o)&&a!=null&&o==="onScroll"&&F("scroll",r)}switch(n){case"input":yr(r),ho(r,i,!0);break;case"textarea":yr(r),xo(r);break;case"select":case"option":break;default:typeof i.onClick=="function"&&(r.onclick=es)}r=s,t.updateQueue=r,r!==null&&(t.flags|=4)}else{o=s.nodeType===9?s:s.ownerDocument,e==="http://www.w3.org/1999/xhtml"&&(e=Qa(n)),e==="http://www.w3.org/1999/xhtml"?n==="script"?(e=o.createElement("div"),e.innerHTML="<script><\/script>",e=e.removeChild(e.firstChild)):typeof r.is=="string"?e=o.createElement(n,{is:r.is}):(e=o.createElement(n),n==="select"&&(o=e,r.multiple?o.multiple=!0:r.size&&(o.size=r.size))):e=o.createElementNS(e,n),e[Ve]=t,e[tr]=r,gc(e,t,!1,!1),t.stateNode=e;e:{switch(o=Sl(n,r),n){case"dialog":F("cancel",e),F("close",e),s=r;break;case"iframe":case"object":case"embed":F("load",e),s=r;break;case"video":case"audio":for(s=0;s<zn.length;s++)F(zn[s],e);s=r;break;case"source":F("error",e),s=r;break;case"img":case"image":case"link":F("error",e),F("load",e),s=r;break;case"details":F("toggle",e),s=r;break;case"input":mo(e,r),s=xl(e,r),F("invalid",e);break;case"option":s=r;break;case"select":e._wrapperState={wasMultiple:!!r.multiple},s=Q({},r,{value:void 0}),F("invalid",e);break;case"textarea":go(e,r),s=wl(e,r),F("invalid",e);break;default:s=r}Nl(n,s),a=s;for(i in a)if(a.hasOwnProperty(i)){var u=a[i];i==="style"?Ya(e,u):i==="dangerouslySetInnerHTML"?(u=u?u.__html:void 0,u!=null&&Ka(e,u)):i==="children"?typeof u=="string"?(n!=="textarea"||u!=="")&&Qn(e,u):typeof u=="number"&&Qn(e,""+u):i!=="suppressContentEditableWarning"&&i!=="suppressHydrationWarning"&&i!=="autoFocus"&&(Wn.hasOwnProperty(i)?u!=null&&i==="onScroll"&&F("scroll",e):u!=null&&xi(e,i,u,o))}switch(n){case"input":yr(e),ho(e,r,!1);break;case"textarea":yr(e),xo(e);break;case"option":r.value!=null&&e.setAttribute("value",""+yt(r.value));break;case"select":e.multiple=!!r.multiple,i=r.value,i!=null?tn(e,!!r.multiple,i,!1):r.defaultValue!=null&&tn(e,!!r.multiple,r.defaultValue,!0);break;default:typeof s.onClick=="function"&&(e.onclick=es)}switch(n){case"button":case"input":case"select":case"textarea":r=!!r.autoFocus;break e;case"img":r=!0;break e;default:r=!1}}r&&(t.flags|=4)}t.ref!==null&&(t.flags|=512,t.flags|=2097152)}return oe(t),null;case 6:if(e&&t.stateNode!=null)yc(e,t,e.memoizedProps,r);else{if(typeof r!="string"&&t.stateNode===null)throw Error(S(166));if(n=At(rr.current),At(Be.current),Lr(t)){if(r=t.stateNode,n=t.memoizedProps,r[Ve]=t,(i=r.nodeValue!==n)&&(e=je,e!==null))switch(e.tag){case 3:Er(r.nodeValue,n,(e.mode&1)!==0);break;case 5:e.memoizedProps.suppressHydrationWarning!==!0&&Er(r.nodeValue,n,(e.mode&1)!==0)}i&&(t.flags|=4)}else r=(n.nodeType===9?n:n.ownerDocument).createTextNode(r),r[Ve]=t,t.stateNode=r}return oe(t),null;case 13:if(V(B),r=t.memoizedState,e===null||e.memoizedState!==null&&e.memoizedState.dehydrated!==null){if(H&&we!==null&&t.mode&1&&!(t.flags&128))Iu(),cn(),t.flags|=98560,i=!1;else if(i=Lr(t),r!==null&&r.dehydrated!==null){if(e===null){if(!i)throw Error(S(318));if(i=t.memoizedState,i=i!==null?i.dehydrated:null,!i)throw Error(S(317));i[Ve]=t}else cn(),!(t.flags&128)&&(t.memoizedState=null),t.flags|=4;oe(t),i=!1}else Ie!==null&&(ri(Ie),Ie=null),i=!0;if(!i)return t.flags&65536?t:null}return t.flags&128?(t.lanes=n,t):(r=r!==null,r!==(e!==null&&e.memoizedState!==null)&&r&&(t.child.flags|=8192,t.mode&1&&(e===null||B.current&1?J===0&&(J=3):qi())),t.updateQueue!==null&&(t.flags|=4),oe(t),null);case 4:return fn(),Yl(e,t),e===null&&qn(t.stateNode.containerInfo),oe(t),null;case 10:return Ii(t.type._context),oe(t),null;case 17:return xe(t.type)&&ts(),oe(t),null;case 19:if(V(B),i=t.memoizedState,i===null)return oe(t),null;if(r=(t.flags&128)!==0,o=i.rendering,o===null)if(r)Tn(i,!1);else{if(J!==0||e!==null&&e.flags&128)for(e=t.child;e!==null;){if(o=as(e),o!==null){for(t.flags|=128,Tn(i,!1),r=o.updateQueue,r!==null&&(t.updateQueue=r,t.flags|=4),t.subtreeFlags=0,r=n,n=t.child;n!==null;)i=n,e=r,i.flags&=14680066,o=i.alternate,o===null?(i.childLanes=0,i.lanes=e,i.child=null,i.subtreeFlags=0,i.memoizedProps=null,i.memoizedState=null,i.updateQueue=null,i.dependencies=null,i.stateNode=null):(i.childLanes=o.childLanes,i.lanes=o.lanes,i.child=o.child,i.subtreeFlags=0,i.deletions=null,i.memoizedProps=o.memoizedProps,i.memoizedState=o.memoizedState,i.updateQueue=o.updateQueue,i.type=o.type,e=o.dependencies,i.dependencies=e===null?null:{lanes:e.lanes,firstContext:e.firstContext}),n=n.sibling;return U(B,B.current&1|2),t.child}e=e.sibling}i.tail!==null&&Y()>mn&&(t.flags|=128,r=!0,Tn(i,!1),t.lanes=4194304)}else{if(!r)if(e=as(o),e!==null){if(t.flags|=128,r=!0,n=e.updateQueue,n!==null&&(t.updateQueue=n,t.flags|=4),Tn(i,!0),i.tail===null&&i.tailMode==="hidden"&&!o.alternate&&!H)return oe(t),null}else 2*Y()-i.renderingStartTime>mn&&n!==1073741824&&(t.flags|=128,r=!0,Tn(i,!1),t.lanes=4194304);i.isBackwards?(o.sibling=t.child,t.child=o):(n=i.last,n!==null?n.sibling=o:t.child=o,i.last=o)}return i.tail!==null?(t=i.tail,i.rendering=t,i.tail=t.sibling,i.renderingStartTime=Y(),t.sibling=null,n=B.current,U(B,r?n&1|2:n&1),t):(oe(t),null);case 22:case 23:return Ji(),r=t.memoizedState!==null,e!==null&&e.memoizedState!==null!==r&&(t.flags|=8192),r&&t.mode&1?ve&1073741824&&(oe(t),t.subtreeFlags&6&&(t.flags|=8192)):oe(t),null;case 24:return null;case 25:return null}throw Error(S(156,t.tag))}function ep(e,t){switch(Ai(t),t.tag){case 1:return xe(t.type)&&ts(),e=t.flags,e&65536?(t.flags=e&-65537|128,t):null;case 3:return fn(),V(ge),V(ue),Fi(),e=t.flags,e&65536&&!(e&128)?(t.flags=e&-65537|128,t):null;case 5:return Ui(t),null;case 13:if(V(B),e=t.memoizedState,e!==null&&e.dehydrated!==null){if(t.alternate===null)throw Error(S(340));cn()}return e=t.flags,e&65536?(t.flags=e&-65537|128,t):null;case 19:return V(B),null;case 4:return fn(),null;case 10:return Ii(t.type._context),null;case 22:case 23:return Ji(),null;case 24:return null;default:return null}}var Tr=!1,ae=!1,tp=typeof WeakSet=="function"?WeakSet:Set,C=null;function qt(e,t){var n=e.ref;if(n!==null)if(typeof n=="function")try{n(null)}catch(r){K(e,t,r)}else n.current=null}function Xl(e,t,n){try{n()}catch(r){K(e,t,r)}}var ia=!1;function np(e,t){if(Pl=Zr,e=Su(),Ti(e)){if("selectionStart"in e)var n={start:e.selectionStart,end:e.selectionEnd};else e:{n=(n=e.ownerDocument)&&n.defaultView||window;var r=n.getSelection&&n.getSelection();if(r&&r.rangeCount!==0){n=r.anchorNode;var s=r.anchorOffset,i=r.focusNode;r=r.focusOffset;try{n.nodeType,i.nodeType}catch{n=null;break e}var o=0,a=-1,u=-1,p=0,h=0,m=e,x=null;t:for(;;){for(var y;m!==n||s!==0&&m.nodeType!==3||(a=o+s),m!==i||r!==0&&m.nodeType!==3||(u=o+r),m.nodeType===3&&(o+=m.nodeValue.length),(y=m.firstChild)!==null;)x=m,m=y;for(;;){if(m===e)break t;if(x===n&&++p===s&&(a=o),x===i&&++h===r&&(u=o),(y=m.nextSibling)!==null)break;m=x,x=m.parentNode}m=y}n=a===-1||u===-1?null:{start:a,end:u}}else n=null}n=n||{start:0,end:0}}else n=null;for(_l={focusedElem:e,selectionRange:n},Zr=!1,C=t;C!==null;)if(t=C,e=t.child,(t.subtreeFlags&1028)!==0&&e!==null)e.return=t,C=e;else for(;C!==null;){t=C;try{var w=t.alternate;if(t.flags&1024)switch(t.tag){case 0:case 11:case 15:break;case 1:if(w!==null){var v=w.memoizedProps,N=w.memoizedState,d=t.stateNode,c=d.getSnapshotBeforeUpdate(t.elementType===t.type?v:_e(t.type,v),N);d.__reactInternalSnapshotBeforeUpdate=c}break;case 3:var f=t.stateNode.containerInfo;f.nodeType===1?f.textContent="":f.nodeType===9&&f.documentElement&&f.removeChild(f.documentElement);break;case 5:case 6:case 4:case 17:break;default:throw Error(S(163))}}catch(g){K(t,t.return,g)}if(e=t.sibling,e!==null){e.return=t.return,C=e;break}C=t.return}return w=ia,ia=!1,w}function Vn(e,t,n){var r=t.updateQueue;if(r=r!==null?r.lastEffect:null,r!==null){var s=r=r.next;do{if((s.tag&e)===e){var i=s.destroy;s.destroy=void 0,i!==void 0&&Xl(t,n,i)}s=s.next}while(s!==r)}}function bs(e,t){if(t=t.updateQueue,t=t!==null?t.lastEffect:null,t!==null){var n=t=t.next;do{if((n.tag&e)===e){var r=n.create;n.destroy=r()}n=n.next}while(n!==t)}}function Zl(e){var t=e.ref;if(t!==null){var n=e.stateNode;switch(e.tag){case 5:e=n;break;default:e=n}typeof t=="function"?t(e):t.current=e}}function vc(e){var t=e.alternate;t!==null&&(e.alternate=null,vc(t)),e.child=null,e.deletions=null,e.sibling=null,e.tag===5&&(t=e.stateNode,t!==null&&(delete t[Ve],delete t[tr],delete t[Rl],delete t[Of],delete t[$f])),e.stateNode=null,e.return=null,e.dependencies=null,e.memoizedProps=null,e.memoizedState=null,e.pendingProps=null,e.stateNode=null,e.updateQueue=null}function wc(e){return e.tag===5||e.tag===3||e.tag===4}function oa(e){e:for(;;){for(;e.sibling===null;){if(e.return===null||wc(e.return))return null;e=e.return}for(e.sibling.return=e.return,e=e.sibling;e.tag!==5&&e.tag!==6&&e.tag!==18;){if(e.flags&2||e.child===null||e.tag===4)continue e;e.child.return=e,e=e.child}if(!(e.flags&2))return e.stateNode}}function Jl(e,t,n){var r=e.tag;if(r===5||r===6)e=e.stateNode,t?n.nodeType===8?n.parentNode.insertBefore(e,t):n.insertBefore(e,t):(n.nodeType===8?(t=n.parentNode,t.insertBefore(e,n)):(t=n,t.appendChild(e)),n=n._reactRootContainer,n!=null||t.onclick!==null||(t.onclick=es));else if(r!==4&&(e=e.child,e!==null))for(Jl(e,t,n),e=e.sibling;e!==null;)Jl(e,t,n),e=e.sibling}function ql(e,t,n){var r=e.tag;if(r===5||r===6)e=e.stateNode,t?n.insertBefore(e,t):n.appendChild(e);else if(r!==4&&(e=e.child,e!==null))for(ql(e,t,n),e=e.sibling;e!==null;)ql(e,t,n),e=e.sibling}var re=null,ze=!1;function nt(e,t,n){for(n=n.child;n!==null;)jc(e,t,n),n=n.sibling}function jc(e,t,n){if(He&&typeof He.onCommitFiberUnmount=="function")try{He.onCommitFiberUnmount(ws,n)}catch{}switch(n.tag){case 5:ae||qt(n,t);case 6:var r=re,s=ze;re=null,nt(e,t,n),re=r,ze=s,re!==null&&(ze?(e=re,n=n.stateNode,e.nodeType===8?e.parentNode.removeChild(n):e.removeChild(n)):re.removeChild(n.stateNode));break;case 18:re!==null&&(ze?(e=re,n=n.stateNode,e.nodeType===8?el(e.parentNode,n):e.nodeType===1&&el(e,n),Xn(e)):el(re,n.stateNode));break;case 4:r=re,s=ze,re=n.stateNode.containerInfo,ze=!0,nt(e,t,n),re=r,ze=s;break;case 0:case 11:case 14:case 15:if(!ae&&(r=n.updateQueue,r!==null&&(r=r.lastEffect,r!==null))){s=r=r.next;do{var i=s,o=i.destroy;i=i.tag,o!==void 0&&(i&2||i&4)&&Xl(n,t,o),s=s.next}while(s!==r)}nt(e,t,n);break;case 1:if(!ae&&(qt(n,t),r=n.stateNode,typeof r.componentWillUnmount=="function"))try{r.props=n.memoizedProps,r.state=n.memoizedState,r.componentWillUnmount()}catch(a){K(n,t,a)}nt(e,t,n);break;case 21:nt(e,t,n);break;case 22:n.mode&1?(ae=(r=ae)||n.memoizedState!==null,nt(e,t,n),ae=r):nt(e,t,n);break;default:nt(e,t,n)}}function aa(e){var t=e.updateQueue;if(t!==null){e.updateQueue=null;var n=e.stateNode;n===null&&(n=e.stateNode=new tp),t.forEach(function(r){var s=dp.bind(null,e,r);n.has(r)||(n.add(r),r.then(s,s))})}}function Pe(e,t){var n=t.deletions;if(n!==null)for(var r=0;r<n.length;r++){var s=n[r];try{var i=e,o=t,a=o;e:for(;a!==null;){switch(a.tag){case 5:re=a.stateNode,ze=!1;break e;case 3:re=a.stateNode.containerInfo,ze=!0;break e;case 4:re=a.stateNode.containerInfo,ze=!0;break e}a=a.return}if(re===null)throw Error(S(160));jc(i,o,s),re=null,ze=!1;var u=s.alternate;u!==null&&(u.return=null),s.return=null}catch(p){K(s,t,p)}}if(t.subtreeFlags&12854)for(t=t.child;t!==null;)Nc(t,e),t=t.sibling}function Nc(e,t){var n=e.alternate,r=e.flags;switch(e.tag){case 0:case 11:case 14:case 15:if(Pe(t,e),Ue(e),r&4){try{Vn(3,e,e.return),bs(3,e)}catch(v){K(e,e.return,v)}try{Vn(5,e,e.return)}catch(v){K(e,e.return,v)}}break;case 1:Pe(t,e),Ue(e),r&512&&n!==null&&qt(n,n.return);break;case 5:if(Pe(t,e),Ue(e),r&512&&n!==null&&qt(n,n.return),e.flags&32){var s=e.stateNode;try{Qn(s,"")}catch(v){K(e,e.return,v)}}if(r&4&&(s=e.stateNode,s!=null)){var i=e.memoizedProps,o=n!==null?n.memoizedProps:i,a=e.type,u=e.updateQueue;if(e.updateQueue=null,u!==null)try{a==="input"&&i.type==="radio"&&i.name!=null&&Ba(s,i),Sl(a,o);var p=Sl(a,i);for(o=0;o<u.length;o+=2){var h=u[o],m=u[o+1];h==="style"?Ya(s,m):h==="dangerouslySetInnerHTML"?Ka(s,m):h==="children"?Qn(s,m):xi(s,h,m,p)}switch(a){case"input":yl(s,i);break;case"textarea":Wa(s,i);break;case"select":var x=s._wrapperState.wasMultiple;s._wrapperState.wasMultiple=!!i.multiple;var y=i.value;y!=null?tn(s,!!i.multiple,y,!1):x!==!!i.multiple&&(i.defaultValue!=null?tn(s,!!i.multiple,i.defaultValue,!0):tn(s,!!i.multiple,i.multiple?[]:"",!1))}s[tr]=i}catch(v){K(e,e.return,v)}}break;case 6:if(Pe(t,e),Ue(e),r&4){if(e.stateNode===null)throw Error(S(162));s=e.stateNode,i=e.memoizedProps;try{s.nodeValue=i}catch(v){K(e,e.return,v)}}break;case 3:if(Pe(t,e),Ue(e),r&4&&n!==null&&n.memoizedState.isDehydrated)try{Xn(t.containerInfo)}catch(v){K(e,e.return,v)}break;case 4:Pe(t,e),Ue(e);break;case 13:Pe(t,e),Ue(e),s=e.child,s.flags&8192&&(i=s.memoizedState!==null,s.stateNode.isHidden=i,!i||s.alternate!==null&&s.alternate.memoizedState!==null||(Xi=Y())),r&4&&aa(e);break;case 22:if(h=n!==null&&n.memoizedState!==null,e.mode&1?(ae=(p=ae)||h,Pe(t,e),ae=p):Pe(t,e),Ue(e),r&8192){if(p=e.memoizedState!==null,(e.stateNode.isHidden=p)&&!h&&e.mode&1)for(C=e,h=e.child;h!==null;){for(m=C=h;C!==null;){switch(x=C,y=x.child,x.tag){case 0:case 11:case 14:case 15:Vn(4,x,x.return);break;case 1:qt(x,x.return);var w=x.stateNode;if(typeof w.componentWillUnmount=="function"){r=x,n=x.return;try{t=r,w.props=t.memoizedProps,w.state=t.memoizedState,w.componentWillUnmount()}catch(v){K(r,n,v)}}break;case 5:qt(x,x.return);break;case 22:if(x.memoizedState!==null){ca(m);continue}}y!==null?(y.return=x,C=y):ca(m)}h=h.sibling}e:for(h=null,m=e;;){if(m.tag===5){if(h===null){h=m;try{s=m.stateNode,p?(i=s.style,typeof i.setProperty=="function"?i.setProperty("display","none","important"):i.display="none"):(a=m.stateNode,u=m.memoizedProps.style,o=u!=null&&u.hasOwnProperty("display")?u.display:null,a.style.display=Ga("display",o))}catch(v){K(e,e.return,v)}}}else if(m.tag===6){if(h===null)try{m.stateNode.nodeValue=p?"":m.memoizedProps}catch(v){K(e,e.return,v)}}else if((m.tag!==22&&m.tag!==23||m.memoizedState===null||m===e)&&m.child!==null){m.child.return=m,m=m.child;continue}if(m===e)break e;for(;m.sibling===null;){if(m.return===null||m.return===e)break e;h===m&&(h=null),m=m.return}h===m&&(h=null),m.sibling.return=m.return,m=m.sibling}}break;case 19:Pe(t,e),Ue(e),r&4&&aa(e);break;case 21:break;default:Pe(t,e),Ue(e)}}function Ue(e){var t=e.flags;if(t&2){try{e:{for(var n=e.return;n!==null;){if(wc(n)){var r=n;break e}n=n.return}throw Error(S(160))}switch(r.tag){case 5:var s=r.stateNode;r.flags&32&&(Qn(s,""),r.flags&=-33);var i=oa(e);ql(e,i,s);break;case 3:case 4:var o=r.stateNode.containerInfo,a=oa(e);Jl(e,a,o);break;default:throw Error(S(161))}}catch(u){K(e,e.return,u)}e.flags&=-3}t&4096&&(e.flags&=-4097)}function rp(e,t,n){C=e,Sc(e)}function Sc(e,t,n){for(var r=(e.mode&1)!==0;C!==null;){var s=C,i=s.child;if(s.tag===22&&r){var o=s.memoizedState!==null||Tr;if(!o){var a=s.alternate,u=a!==null&&a.memoizedState!==null||ae;a=Tr;var p=ae;if(Tr=o,(ae=u)&&!p)for(C=s;C!==null;)o=C,u=o.child,o.tag===22&&o.memoizedState!==null?da(s):u!==null?(u.return=o,C=u):da(s);for(;i!==null;)C=i,Sc(i),i=i.sibling;C=s,Tr=a,ae=p}ua(e)}else s.subtreeFlags&8772&&i!==null?(i.return=s,C=i):ua(e)}}function ua(e){for(;C!==null;){var t=C;if(t.flags&8772){var n=t.alternate;try{if(t.flags&8772)switch(t.tag){case 0:case 11:case 15:ae||bs(5,t);break;case 1:var r=t.stateNode;if(t.flags&4&&!ae)if(n===null)r.componentDidMount();else{var s=t.elementType===t.type?n.memoizedProps:_e(t.type,n.memoizedProps);r.componentDidUpdate(s,n.memoizedState,r.__reactInternalSnapshotBeforeUpdate)}var i=t.updateQueue;i!==null&&Ko(t,i,r);break;case 3:var o=t.updateQueue;if(o!==null){if(n=null,t.child!==null)switch(t.child.tag){case 5:n=t.child.stateNode;break;case 1:n=t.child.stateNode}Ko(t,o,n)}break;case 5:var a=t.stateNode;if(n===null&&t.flags&4){n=a;var u=t.memoizedProps;switch(t.type){case"button":case"input":case"select":case"textarea":u.autoFocus&&n.focus();break;case"img":u.src&&(n.src=u.src)}}break;case 6:break;case 4:break;case 12:break;case 13:if(t.memoizedState===null){var p=t.alternate;if(p!==null){var h=p.memoizedState;if(h!==null){var m=h.dehydrated;m!==null&&Xn(m)}}}break;case 19:case 17:case 21:case 22:case 23:case 25:break;default:throw Error(S(163))}ae||t.flags&512&&Zl(t)}catch(x){K(t,t.return,x)}}if(t===e){C=null;break}if(n=t.sibling,n!==null){n.return=t.return,C=n;break}C=t.return}}function ca(e){for(;C!==null;){var t=C;if(t===e){C=null;break}var n=t.sibling;if(n!==null){n.return=t.return,C=n;break}C=t.return}}function da(e){for(;C!==null;){var t=C;try{switch(t.tag){case 0:case 11:case 15:var n=t.return;try{bs(4,t)}catch(u){K(t,n,u)}break;case 1:var r=t.stateNode;if(typeof r.componentDidMount=="function"){var s=t.return;try{r.componentDidMount()}catch(u){K(t,s,u)}}var i=t.return;try{Zl(t)}catch(u){K(t,i,u)}break;case 5:var o=t.return;try{Zl(t)}catch(u){K(t,o,u)}}}catch(u){K(t,t.return,u)}if(t===e){C=null;break}var a=t.sibling;if(a!==null){a.return=t.return,C=a;break}C=t.return}}var sp=Math.ceil,ds=tt.ReactCurrentDispatcher,Gi=tt.ReactCurrentOwner,Me=tt.ReactCurrentBatchConfig,z=0,te=null,X=null,se=0,ve=0,en=jt(0),J=0,or=null,Ot=0,Ms=0,Yi=0,Hn=null,me=null,Xi=0,mn=1/0,We=null,fs=!1,ei=null,mt=null,Dr=!1,at=null,ps=0,Bn=0,ti=null,Hr=-1,Br=0;function de(){return z&6?Y():Hr!==-1?Hr:Hr=Y()}function ht(e){return e.mode&1?z&2&&se!==0?se&-se:Ff.transition!==null?(Br===0&&(Br=ou()),Br):(e=R,e!==0||(e=window.event,e=e===void 0?16:mu(e.type)),e):1}function Oe(e,t,n,r){if(50<Bn)throw Bn=0,ti=null,Error(S(185));cr(e,n,r),(!(z&2)||e!==te)&&(e===te&&(!(z&2)&&(Ms|=n),J===4&&it(e,se)),ye(e,r),n===1&&z===0&&!(t.mode&1)&&(mn=Y()+500,Cs&&Nt()))}function ye(e,t){var n=e.callbackNode;Ud(e,t);var r=Xr(e,e===te?se:0);if(r===0)n!==null&&wo(n),e.callbackNode=null,e.callbackPriority=0;else if(t=r&-r,e.callbackPriority!==t){if(n!=null&&wo(n),t===1)e.tag===0?Uf(fa.bind(null,e)):Pu(fa.bind(null,e)),If(function(){!(z&6)&&Nt()}),n=null;else{switch(au(r)){case 1:n=Ni;break;case 4:n=lu;break;case 16:n=Yr;break;case 536870912:n=iu;break;default:n=Yr}n=Dc(n,kc.bind(null,e))}e.callbackPriority=t,e.callbackNode=n}}function kc(e,t){if(Hr=-1,Br=0,z&6)throw Error(S(327));var n=e.callbackNode;if(on()&&e.callbackNode!==n)return null;var r=Xr(e,e===te?se:0);if(r===0)return null;if(r&30||r&e.expiredLanes||t)t=ms(e,r);else{t=r;var s=z;z|=2;var i=Ec();(te!==e||se!==t)&&(We=null,mn=Y()+500,Pt(e,t));do try{op();break}catch(a){Cc(e,a)}while(1);zi(),ds.current=i,z=s,X!==null?t=0:(te=null,se=0,t=J)}if(t!==0){if(t===2&&(s=bl(e),s!==0&&(r=s,t=ni(e,s))),t===1)throw n=or,Pt(e,0),it(e,r),ye(e,Y()),n;if(t===6)it(e,r);else{if(s=e.current.alternate,!(r&30)&&!lp(s)&&(t=ms(e,r),t===2&&(i=bl(e),i!==0&&(r=i,t=ni(e,i))),t===1))throw n=or,Pt(e,0),it(e,r),ye(e,Y()),n;switch(e.finishedWork=s,e.finishedLanes=r,t){case 0:case 1:throw Error(S(345));case 2:Lt(e,me,We);break;case 3:if(it(e,r),(r&130023424)===r&&(t=Xi+500-Y(),10<t)){if(Xr(e,0)!==0)break;if(s=e.suspendedLanes,(s&r)!==r){de(),e.pingedLanes|=e.suspendedLanes&s;break}e.timeoutHandle=Il(Lt.bind(null,e,me,We),t);break}Lt(e,me,We);break;case 4:if(it(e,r),(r&4194240)===r)break;for(t=e.eventTimes,s=-1;0<r;){var o=31-Re(r);i=1<<o,o=t[o],o>s&&(s=o),r&=~i}if(r=s,r=Y()-r,r=(120>r?120:480>r?480:1080>r?1080:1920>r?1920:3e3>r?3e3:4320>r?4320:1960*sp(r/1960))-r,10<r){e.timeoutHandle=Il(Lt.bind(null,e,me,We),r);break}Lt(e,me,We);break;case 5:Lt(e,me,We);break;default:throw Error(S(329))}}}return ye(e,Y()),e.callbackNode===n?kc.bind(null,e):null}function ni(e,t){var n=Hn;return e.current.memoizedState.isDehydrated&&(Pt(e,t).flags|=256),e=ms(e,t),e!==2&&(t=me,me=n,t!==null&&ri(t)),e}function ri(e){me===null?me=e:me.push.apply(me,e)}function lp(e){for(var t=e;;){if(t.flags&16384){var n=t.updateQueue;if(n!==null&&(n=n.stores,n!==null))for(var r=0;r<n.length;r++){var s=n[r],i=s.getSnapshot;s=s.value;try{if(!$e(i(),s))return!1}catch{return!1}}}if(n=t.child,t.subtreeFlags&16384&&n!==null)n.return=t,t=n;else{if(t===e)break;for(;t.sibling===null;){if(t.return===null||t.return===e)return!0;t=t.return}t.sibling.return=t.return,t=t.sibling}}return!0}function it(e,t){for(t&=~Yi,t&=~Ms,e.suspendedLanes|=t,e.pingedLanes&=~t,e=e.expirationTimes;0<t;){var n=31-Re(t),r=1<<n;e[n]=-1,t&=~r}}function fa(e){if(z&6)throw Error(S(327));on();var t=Xr(e,0);if(!(t&1))return ye(e,Y()),null;var n=ms(e,t);if(e.tag!==0&&n===2){var r=bl(e);r!==0&&(t=r,n=ni(e,r))}if(n===1)throw n=or,Pt(e,0),it(e,t),ye(e,Y()),n;if(n===6)throw Error(S(345));return e.finishedWork=e.current.alternate,e.finishedLanes=t,Lt(e,me,We),ye(e,Y()),null}function Zi(e,t){var n=z;z|=1;try{return e(t)}finally{z=n,z===0&&(mn=Y()+500,Cs&&Nt())}}function $t(e){at!==null&&at.tag===0&&!(z&6)&&on();var t=z;z|=1;var n=Me.transition,r=R;try{if(Me.transition=null,R=1,e)return e()}finally{R=r,Me.transition=n,z=t,!(z&6)&&Nt()}}function Ji(){ve=en.current,V(en)}function Pt(e,t){e.finishedWork=null,e.finishedLanes=0;var n=e.timeoutHandle;if(n!==-1&&(e.timeoutHandle=-1,zf(n)),X!==null)for(n=X.return;n!==null;){var r=n;switch(Ai(r),r.tag){case 1:r=r.type.childContextTypes,r!=null&&ts();break;case 3:fn(),V(ge),V(ue),Fi();break;case 5:Ui(r);break;case 4:fn();break;case 13:V(B);break;case 19:V(B);break;case 10:Ii(r.type._context);break;case 22:case 23:Ji()}n=n.return}if(te=e,X=e=gt(e.current,null),se=ve=t,J=0,or=null,Yi=Ms=Ot=0,me=Hn=null,Dt!==null){for(t=0;t<Dt.length;t++)if(n=Dt[t],r=n.interleaved,r!==null){n.interleaved=null;var s=r.next,i=n.pending;if(i!==null){var o=i.next;i.next=s,r.next=o}n.pending=r}Dt=null}return e}function Cc(e,t){do{var n=X;try{if(zi(),Ur.current=cs,us){for(var r=W.memoizedState;r!==null;){var s=r.queue;s!==null&&(s.pending=null),r=r.next}us=!1}if(Rt=0,ee=Z=W=null,Fn=!1,sr=0,Gi.current=null,n===null||n.return===null){J=1,or=t,X=null;break}e:{var i=e,o=n.return,a=n,u=t;if(t=se,a.flags|=32768,u!==null&&typeof u=="object"&&typeof u.then=="function"){var p=u,h=a,m=h.tag;if(!(h.mode&1)&&(m===0||m===11||m===15)){var x=h.alternate;x?(h.updateQueue=x.updateQueue,h.memoizedState=x.memoizedState,h.lanes=x.lanes):(h.updateQueue=null,h.memoizedState=null)}var y=qo(o);if(y!==null){y.flags&=-257,ea(y,o,a,i,t),y.mode&1&&Jo(i,p,t),t=y,u=p;var w=t.updateQueue;if(w===null){var v=new Set;v.add(u),t.updateQueue=v}else w.add(u);break e}else{if(!(t&1)){Jo(i,p,t),qi();break e}u=Error(S(426))}}else if(H&&a.mode&1){var N=qo(o);if(N!==null){!(N.flags&65536)&&(N.flags|=256),ea(N,o,a,i,t),Pi(pn(u,a));break e}}i=u=pn(u,a),J!==4&&(J=2),Hn===null?Hn=[i]:Hn.push(i),i=o;do{switch(i.tag){case 3:i.flags|=65536,t&=-t,i.lanes|=t;var d=ac(i,u,t);Qo(i,d);break e;case 1:a=u;var c=i.type,f=i.stateNode;if(!(i.flags&128)&&(typeof c.getDerivedStateFromError=="function"||f!==null&&typeof f.componentDidCatch=="function"&&(mt===null||!mt.has(f)))){i.flags|=65536,t&=-t,i.lanes|=t;var g=uc(i,a,t);Qo(i,g);break e}}i=i.return}while(i!==null)}bc(n)}catch(j){t=j,X===n&&n!==null&&(X=n=n.return);continue}break}while(1)}function Ec(){var e=ds.current;return ds.current=cs,e===null?cs:e}function qi(){(J===0||J===3||J===2)&&(J=4),te===null||!(Ot&268435455)&&!(Ms&268435455)||it(te,se)}function ms(e,t){var n=z;z|=2;var r=Ec();(te!==e||se!==t)&&(We=null,Pt(e,t));do try{ip();break}catch(s){Cc(e,s)}while(1);if(zi(),z=n,ds.current=r,X!==null)throw Error(S(261));return te=null,se=0,J}function ip(){for(;X!==null;)Lc(X)}function op(){for(;X!==null&&!Dd();)Lc(X)}function Lc(e){var t=Tc(e.alternate,e,ve);e.memoizedProps=e.pendingProps,t===null?bc(e):X=t,Gi.current=null}function bc(e){var t=e;do{var n=t.alternate;if(e=t.return,t.flags&32768){if(n=ep(n,t),n!==null){n.flags&=32767,X=n;return}if(e!==null)e.flags|=32768,e.subtreeFlags=0,e.deletions=null;else{J=6,X=null;return}}else if(n=qf(n,t,ve),n!==null){X=n;return}if(t=t.sibling,t!==null){X=t;return}X=t=e}while(t!==null);J===0&&(J=5)}function Lt(e,t,n){var r=R,s=Me.transition;try{Me.transition=null,R=1,ap(e,t,n,r)}finally{Me.transition=s,R=r}return null}function ap(e,t,n,r){do on();while(at!==null);if(z&6)throw Error(S(327));n=e.finishedWork;var s=e.finishedLanes;if(n===null)return null;if(e.finishedWork=null,e.finishedLanes=0,n===e.current)throw Error(S(177));e.callbackNode=null,e.callbackPriority=0;var i=n.lanes|n.childLanes;if(Fd(e,i),e===te&&(X=te=null,se=0),!(n.subtreeFlags&2064)&&!(n.flags&2064)||Dr||(Dr=!0,Dc(Yr,function(){return on(),null})),i=(n.flags&15990)!==0,n.subtreeFlags&15990||i){i=Me.transition,Me.transition=null;var o=R;R=1;var a=z;z|=4,Gi.current=null,np(e,n),Nc(n,e),bf(_l),Zr=!!Pl,_l=Pl=null,e.current=n,rp(n),Ad(),z=a,R=o,Me.transition=i}else e.current=n;if(Dr&&(Dr=!1,at=e,ps=s),i=e.pendingLanes,i===0&&(mt=null),zd(n.stateNode),ye(e,Y()),t!==null)for(r=e.onRecoverableError,n=0;n<t.length;n++)s=t[n],r(s.value,{componentStack:s.stack,digest:s.digest});if(fs)throw fs=!1,e=ei,ei=null,e;return ps&1&&e.tag!==0&&on(),i=e.pendingLanes,i&1?e===ti?Bn++:(Bn=0,ti=e):Bn=0,Nt(),null}function on(){if(at!==null){var e=au(ps),t=Me.transition,n=R;try{if(Me.transition=null,R=16>e?16:e,at===null)var r=!1;else{if(e=at,at=null,ps=0,z&6)throw Error(S(331));var s=z;for(z|=4,C=e.current;C!==null;){var i=C,o=i.child;if(C.flags&16){var a=i.deletions;if(a!==null){for(var u=0;u<a.length;u++){var p=a[u];for(C=p;C!==null;){var h=C;switch(h.tag){case 0:case 11:case 15:Vn(8,h,i)}var m=h.child;if(m!==null)m.return=h,C=m;else for(;C!==null;){h=C;var x=h.sibling,y=h.return;if(vc(h),h===p){C=null;break}if(x!==null){x.return=y,C=x;break}C=y}}}var w=i.alternate;if(w!==null){var v=w.child;if(v!==null){w.child=null;do{var N=v.sibling;v.sibling=null,v=N}while(v!==null)}}C=i}}if(i.subtreeFlags&2064&&o!==null)o.return=i,C=o;else e:for(;C!==null;){if(i=C,i.flags&2048)switch(i.tag){case 0:case 11:case 15:Vn(9,i,i.return)}var d=i.sibling;if(d!==null){d.return=i.return,C=d;break e}C=i.return}}var c=e.current;for(C=c;C!==null;){o=C;var f=o.child;if(o.subtreeFlags&2064&&f!==null)f.return=o,C=f;else e:for(o=c;C!==null;){if(a=C,a.flags&2048)try{switch(a.tag){case 0:case 11:case 15:bs(9,a)}}catch(j){K(a,a.return,j)}if(a===o){C=null;break e}var g=a.sibling;if(g!==null){g.return=a.return,C=g;break e}C=a.return}}if(z=s,Nt(),He&&typeof He.onPostCommitFiberRoot=="function")try{He.onPostCommitFiberRoot(ws,e)}catch{}r=!0}return r}finally{R=n,Me.transition=t}}return!1}function pa(e,t,n){t=pn(n,t),t=ac(e,t,1),e=pt(e,t,1),t=de(),e!==null&&(cr(e,1,t),ye(e,t))}function K(e,t,n){if(e.tag===3)pa(e,e,n);else for(;t!==null;){if(t.tag===3){pa(t,e,n);break}else if(t.tag===1){var r=t.stateNode;if(typeof t.type.getDerivedStateFromError=="function"||typeof r.componentDidCatch=="function"&&(mt===null||!mt.has(r))){e=pn(n,e),e=uc(t,e,1),t=pt(t,e,1),e=de(),t!==null&&(cr(t,1,e),ye(t,e));break}}t=t.return}}function up(e,t,n){var r=e.pingCache;r!==null&&r.delete(t),t=de(),e.pingedLanes|=e.suspendedLanes&n,te===e&&(se&n)===n&&(J===4||J===3&&(se&130023424)===se&&500>Y()-Xi?Pt(e,0):Yi|=n),ye(e,t)}function Mc(e,t){t===0&&(e.mode&1?(t=jr,jr<<=1,!(jr&130023424)&&(jr=4194304)):t=1);var n=de();e=qe(e,t),e!==null&&(cr(e,t,n),ye(e,n))}function cp(e){var t=e.memoizedState,n=0;t!==null&&(n=t.retryLane),Mc(e,n)}function dp(e,t){var n=0;switch(e.tag){case 13:var r=e.stateNode,s=e.memoizedState;s!==null&&(n=s.retryLane);break;case 19:r=e.stateNode;break;default:throw Error(S(314))}r!==null&&r.delete(t),Mc(e,n)}var Tc;Tc=function(e,t,n){if(e!==null)if(e.memoizedProps!==t.pendingProps||ge.current)he=!0;else{if(!(e.lanes&n)&&!(t.flags&128))return he=!1,Jf(e,t,n);he=!!(e.flags&131072)}else he=!1,H&&t.flags&1048576&&_u(t,ss,t.index);switch(t.lanes=0,t.tag){case 2:var r=t.type;Vr(e,t),e=t.pendingProps;var s=un(t,ue.current);ln(t,n),s=Hi(null,t,r,e,s,n);var i=Bi();return t.flags|=1,typeof s=="object"&&s!==null&&typeof s.render=="function"&&s.$$typeof===void 0?(t.tag=1,t.memoizedState=null,t.updateQueue=null,xe(r)?(i=!0,ns(t)):i=!1,t.memoizedState=s.state!==null&&s.state!==void 0?s.state:null,Oi(t),s.updater=Ls,t.stateNode=s,s._reactInternals=t,Hl(t,r,e,n),t=Ql(null,t,r,!0,i,n)):(t.tag=0,H&&i&&Di(t),ce(null,t,s,n),t=t.child),t;case 16:r=t.elementType;e:{switch(Vr(e,t),e=t.pendingProps,s=r._init,r=s(r._payload),t.type=r,s=t.tag=pp(r),e=_e(r,e),s){case 0:t=Wl(null,t,r,e,n);break e;case 1:t=ra(null,t,r,e,n);break e;case 11:t=ta(null,t,r,e,n);break e;case 14:t=na(null,t,r,_e(r.type,e),n);break e}throw Error(S(306,r,""))}return t;case 0:return r=t.type,s=t.pendingProps,s=t.elementType===r?s:_e(r,s),Wl(e,t,r,s,n);case 1:return r=t.type,s=t.pendingProps,s=t.elementType===r?s:_e(r,s),ra(e,t,r,s,n);case 3:e:{if(pc(t),e===null)throw Error(S(387));r=t.pendingProps,i=t.memoizedState,s=i.element,Uu(e,t),os(t,r,null,n);var o=t.memoizedState;if(r=o.element,i.isDehydrated)if(i={element:r,isDehydrated:!1,cache:o.cache,pendingSuspenseBoundaries:o.pendingSuspenseBoundaries,transitions:o.transitions},t.updateQueue.baseState=i,t.memoizedState=i,t.flags&256){s=pn(Error(S(423)),t),t=sa(e,t,r,n,s);break e}else if(r!==s){s=pn(Error(S(424)),t),t=sa(e,t,r,n,s);break e}else for(we=ft(t.stateNode.containerInfo.firstChild),je=t,H=!0,Ie=null,n=Ou(t,null,r,n),t.child=n;n;)n.flags=n.flags&-3|4096,n=n.sibling;else{if(cn(),r===s){t=et(e,t,n);break e}ce(e,t,r,n)}t=t.child}return t;case 5:return Fu(t),e===null&&Ul(t),r=t.type,s=t.pendingProps,i=e!==null?e.memoizedProps:null,o=s.children,zl(r,s)?o=null:i!==null&&zl(r,i)&&(t.flags|=32),fc(e,t),ce(e,t,o,n),t.child;case 6:return e===null&&Ul(t),null;case 13:return mc(e,t,n);case 4:return $i(t,t.stateNode.containerInfo),r=t.pendingProps,e===null?t.child=dn(t,null,r,n):ce(e,t,r,n),t.child;case 11:return r=t.type,s=t.pendingProps,s=t.elementType===r?s:_e(r,s),ta(e,t,r,s,n);case 7:return ce(e,t,t.pendingProps,n),t.child;case 8:return ce(e,t,t.pendingProps.children,n),t.child;case 12:return ce(e,t,t.pendingProps.children,n),t.child;case 10:e:{if(r=t.type._context,s=t.pendingProps,i=t.memoizedProps,o=s.value,U(ls,r._currentValue),r._currentValue=o,i!==null)if($e(i.value,o)){if(i.children===s.children&&!ge.current){t=et(e,t,n);break e}}else for(i=t.child,i!==null&&(i.return=t);i!==null;){var a=i.dependencies;if(a!==null){o=i.child;for(var u=a.firstContext;u!==null;){if(u.context===r){if(i.tag===1){u=Ye(-1,n&-n),u.tag=2;var p=i.updateQueue;if(p!==null){p=p.shared;var h=p.pending;h===null?u.next=u:(u.next=h.next,h.next=u),p.pending=u}}i.lanes|=n,u=i.alternate,u!==null&&(u.lanes|=n),Fl(i.return,n,t),a.lanes|=n;break}u=u.next}}else if(i.tag===10)o=i.type===t.type?null:i.child;else if(i.tag===18){if(o=i.return,o===null)throw Error(S(341));o.lanes|=n,a=o.alternate,a!==null&&(a.lanes|=n),Fl(o,n,t),o=i.sibling}else o=i.child;if(o!==null)o.return=i;else for(o=i;o!==null;){if(o===t){o=null;break}if(i=o.sibling,i!==null){i.return=o.return,o=i;break}o=o.return}i=o}ce(e,t,s.children,n),t=t.child}return t;case 9:return s=t.type,r=t.pendingProps.children,ln(t,n),s=Te(s),r=r(s),t.flags|=1,ce(e,t,r,n),t.child;case 14:return r=t.type,s=_e(r,t.pendingProps),s=_e(r.type,s),na(e,t,r,s,n);case 15:return cc(e,t,t.type,t.pendingProps,n);case 17:return r=t.type,s=t.pendingProps,s=t.elementType===r?s:_e(r,s),Vr(e,t),t.tag=1,xe(r)?(e=!0,ns(t)):e=!1,ln(t,n),oc(t,r,s),Hl(t,r,s,n),Ql(null,t,r,!0,e,n);case 19:return hc(e,t,n);case 22:return dc(e,t,n)}throw Error(S(156,t.tag))};function Dc(e,t){return su(e,t)}function fp(e,t,n,r){this.tag=e,this.key=n,this.sibling=this.child=this.return=this.stateNode=this.type=this.elementType=null,this.index=0,this.ref=null,this.pendingProps=t,this.dependencies=this.memoizedState=this.updateQueue=this.memoizedProps=null,this.mode=r,this.subtreeFlags=this.flags=0,this.deletions=null,this.childLanes=this.lanes=0,this.alternate=null}function be(e,t,n,r){return new fp(e,t,n,r)}function eo(e){return e=e.prototype,!(!e||!e.isReactComponent)}function pp(e){if(typeof e=="function")return eo(e)?1:0;if(e!=null){if(e=e.$$typeof,e===vi)return 11;if(e===wi)return 14}return 2}function gt(e,t){var n=e.alternate;return n===null?(n=be(e.tag,t,e.key,e.mode),n.elementType=e.elementType,n.type=e.type,n.stateNode=e.stateNode,n.alternate=e,e.alternate=n):(n.pendingProps=t,n.type=e.type,n.flags=0,n.subtreeFlags=0,n.deletions=null),n.flags=e.flags&14680064,n.childLanes=e.childLanes,n.lanes=e.lanes,n.child=e.child,n.memoizedProps=e.memoizedProps,n.memoizedState=e.memoizedState,n.updateQueue=e.updateQueue,t=e.dependencies,n.dependencies=t===null?null:{lanes:t.lanes,firstContext:t.firstContext},n.sibling=e.sibling,n.index=e.index,n.ref=e.ref,n}function Wr(e,t,n,r,s,i){var o=2;if(r=e,typeof e=="function")eo(e)&&(o=1);else if(typeof e=="string")o=5;else e:switch(e){case Bt:return _t(n.children,s,i,t);case yi:o=8,s|=8;break;case pl:return e=be(12,n,t,s|2),e.elementType=pl,e.lanes=i,e;case ml:return e=be(13,n,t,s),e.elementType=ml,e.lanes=i,e;case hl:return e=be(19,n,t,s),e.elementType=hl,e.lanes=i,e;case Fa:return Ts(n,s,i,t);default:if(typeof e=="object"&&e!==null)switch(e.$$typeof){case $a:o=10;break e;case Ua:o=9;break e;case vi:o=11;break e;case wi:o=14;break e;case rt:o=16,r=null;break e}throw Error(S(130,e==null?e:typeof e,""))}return t=be(o,n,t,s),t.elementType=e,t.type=r,t.lanes=i,t}function _t(e,t,n,r){return e=be(7,e,r,t),e.lanes=n,e}function Ts(e,t,n,r){return e=be(22,e,r,t),e.elementType=Fa,e.lanes=n,e.stateNode={isHidden:!1},e}function al(e,t,n){return e=be(6,e,null,t),e.lanes=n,e}function ul(e,t,n){return t=be(4,e.children!==null?e.children:[],e.key,t),t.lanes=n,t.stateNode={containerInfo:e.containerInfo,pendingChildren:null,implementation:e.implementation},t}function mp(e,t,n,r,s){this.tag=t,this.containerInfo=e,this.finishedWork=this.pingCache=this.current=this.pendingChildren=null,this.timeoutHandle=-1,this.callbackNode=this.pendingContext=this.context=null,this.callbackPriority=0,this.eventTimes=Hs(0),this.expirationTimes=Hs(-1),this.entangledLanes=this.finishedLanes=this.mutableReadLanes=this.expiredLanes=this.pingedLanes=this.suspendedLanes=this.pendingLanes=0,this.entanglements=Hs(0),this.identifierPrefix=r,this.onRecoverableError=s,this.mutableSourceEagerHydrationData=null}function to(e,t,n,r,s,i,o,a,u){return e=new mp(e,t,n,a,u),t===1?(t=1,i===!0&&(t|=8)):t=0,i=be(3,null,null,t),e.current=i,i.stateNode=e,i.memoizedState={element:r,isDehydrated:n,cache:null,transitions:null,pendingSuspenseBoundaries:null},Oi(i),e}function hp(e,t,n){var r=3<arguments.length&&arguments[3]!==void 0?arguments[3]:null;return{$$typeof:Ht,key:r==null?null:""+r,children:e,containerInfo:t,implementation:n}}function Ac(e){if(!e)return vt;e=e._reactInternals;e:{if(Ft(e)!==e||e.tag!==1)throw Error(S(170));var t=e;do{switch(t.tag){case 3:t=t.stateNode.context;break e;case 1:if(xe(t.type)){t=t.stateNode.__reactInternalMemoizedMergedChildContext;break e}}t=t.return}while(t!==null);throw Error(S(171))}if(e.tag===1){var n=e.type;if(xe(n))return Au(e,n,t)}return t}function Pc(e,t,n,r,s,i,o,a,u){return e=to(n,r,!0,e,s,i,o,a,u),e.context=Ac(null),n=e.current,r=de(),s=ht(n),i=Ye(r,s),i.callback=t??null,pt(n,i,s),e.current.lanes=s,cr(e,s,r),ye(e,r),e}function Ds(e,t,n,r){var s=t.current,i=de(),o=ht(s);return n=Ac(n),t.context===null?t.context=n:t.pendingContext=n,t=Ye(i,o),t.payload={element:e},r=r===void 0?null:r,r!==null&&(t.callback=r),e=pt(s,t,o),e!==null&&(Oe(e,s,o,i),$r(e,s,o)),o}function hs(e){if(e=e.current,!e.child)return null;switch(e.child.tag){case 5:return e.child.stateNode;default:return e.child.stateNode}}function ma(e,t){if(e=e.memoizedState,e!==null&&e.dehydrated!==null){var n=e.retryLane;e.retryLane=n!==0&&n<t?n:t}}function no(e,t){ma(e,t),(e=e.alternate)&&ma(e,t)}function gp(){return null}var _c=typeof reportError=="function"?reportError:function(e){console.error(e)};function ro(e){this._internalRoot=e}As.prototype.render=ro.prototype.render=function(e){var t=this._internalRoot;if(t===null)throw Error(S(409));Ds(e,t,null,null)};As.prototype.unmount=ro.prototype.unmount=function(){var e=this._internalRoot;if(e!==null){this._internalRoot=null;var t=e.containerInfo;$t(function(){Ds(null,e,null,null)}),t[Je]=null}};function As(e){this._internalRoot=e}As.prototype.unstable_scheduleHydration=function(e){if(e){var t=du();e={blockedOn:null,target:e,priority:t};for(var n=0;n<lt.length&&t!==0&&t<lt[n].priority;n++);lt.splice(n,0,e),n===0&&pu(e)}};function so(e){return!(!e||e.nodeType!==1&&e.nodeType!==9&&e.nodeType!==11)}function Ps(e){return!(!e||e.nodeType!==1&&e.nodeType!==9&&e.nodeType!==11&&(e.nodeType!==8||e.nodeValue!==" react-mount-point-unstable "))}function ha(){}function xp(e,t,n,r,s){if(s){if(typeof r=="function"){var i=r;r=function(){var p=hs(o);i.call(p)}}var o=Pc(t,r,e,0,null,!1,!1,"",ha);return e._reactRootContainer=o,e[Je]=o.current,qn(e.nodeType===8?e.parentNode:e),$t(),o}for(;s=e.lastChild;)e.removeChild(s);if(typeof r=="function"){var a=r;r=function(){var p=hs(u);a.call(p)}}var u=to(e,0,!1,null,null,!1,!1,"",ha);return e._reactRootContainer=u,e[Je]=u.current,qn(e.nodeType===8?e.parentNode:e),$t(function(){Ds(t,u,n,r)}),u}function _s(e,t,n,r,s){var i=n._reactRootContainer;if(i){var o=i;if(typeof s=="function"){var a=s;s=function(){var u=hs(o);a.call(u)}}Ds(t,o,e,s)}else o=xp(n,t,e,s,r);return hs(o)}uu=function(e){switch(e.tag){case 3:var t=e.stateNode;if(t.current.memoizedState.isDehydrated){var n=_n(t.pendingLanes);n!==0&&(Si(t,n|1),ye(t,Y()),!(z&6)&&(mn=Y()+500,Nt()))}break;case 13:$t(function(){var r=qe(e,1);if(r!==null){var s=de();Oe(r,e,1,s)}}),no(e,1)}};ki=function(e){if(e.tag===13){var t=qe(e,134217728);if(t!==null){var n=de();Oe(t,e,134217728,n)}no(e,134217728)}};cu=function(e){if(e.tag===13){var t=ht(e),n=qe(e,t);if(n!==null){var r=de();Oe(n,e,t,r)}no(e,t)}};du=function(){return R};fu=function(e,t){var n=R;try{return R=e,t()}finally{R=n}};Cl=function(e,t,n){switch(t){case"input":if(yl(e,n),t=n.name,n.type==="radio"&&t!=null){for(n=e;n.parentNode;)n=n.parentNode;for(n=n.querySelectorAll("input[name="+JSON.stringify(""+t)+'][type="radio"]'),t=0;t<n.length;t++){var r=n[t];if(r!==e&&r.form===e.form){var s=ks(r);if(!s)throw Error(S(90));Ha(r),yl(r,s)}}}break;case"textarea":Wa(e,n);break;case"select":t=n.value,t!=null&&tn(e,!!n.multiple,t,!1)}};Ja=Zi;qa=$t;var yp={usingClientEntryPoint:!1,Events:[fr,Gt,ks,Xa,Za,Zi]},Dn={findFiberByHostInstance:Tt,bundleType:0,version:"18.3.1",rendererPackageName:"react-dom"},vp={bundleType:Dn.bundleType,version:Dn.version,rendererPackageName:Dn.rendererPackageName,rendererConfig:Dn.rendererConfig,overrideHookState:null,overrideHookStateDeletePath:null,overrideHookStateRenamePath:null,overrideProps:null,overridePropsDeletePath:null,overridePropsRenamePath:null,setErrorHandler:null,setSuspenseHandler:null,scheduleUpdate:null,currentDispatcherRef:tt.ReactCurrentDispatcher,findHostInstanceByFiber:function(e){return e=nu(e),e===null?null:e.stateNode},findFiberByHostInstance:Dn.findFiberByHostInstance||gp,findHostInstancesForRefresh:null,scheduleRefresh:null,scheduleRoot:null,setRefreshHandler:null,getCurrentFiber:null,reconcilerVersion:"18.3.1-next-f1338f8080-20240426"};if(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__<"u"){var Ar=__REACT_DEVTOOLS_GLOBAL_HOOK__;if(!Ar.isDisabled&&Ar.supportsFiber)try{ws=Ar.inject(vp),He=Ar}catch{}}Se.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED=yp;Se.createPortal=function(e,t){var n=2<arguments.length&&arguments[2]!==void 0?arguments[2]:null;if(!so(t))throw Error(S(200));return hp(e,t,null,n)};Se.createRoot=function(e,t){if(!so(e))throw Error(S(299));var n=!1,r="",s=_c;return t!=null&&(t.unstable_strictMode===!0&&(n=!0),t.identifierPrefix!==void 0&&(r=t.identifierPrefix),t.onRecoverableError!==void 0&&(s=t.onRecoverableError)),t=to(e,1,!1,null,null,n,!1,r,s),e[Je]=t.current,qn(e.nodeType===8?e.parentNode:e),new ro(t)};Se.findDOMNode=function(e){if(e==null)return null;if(e.nodeType===1)return e;var t=e._reactInternals;if(t===void 0)throw typeof e.render=="function"?Error(S(188)):(e=Object.keys(e).join(","),Error(S(268,e)));return e=nu(t),e=e===null?null:e.stateNode,e};Se.flushSync=function(e){return $t(e)};Se.hydrate=function(e,t,n){if(!Ps(t))throw Error(S(200));return _s(null,e,t,!0,n)};Se.hydrateRoot=function(e,t,n){if(!so(e))throw Error(S(405));var r=n!=null&&n.hydratedSources||null,s=!1,i="",o=_c;if(n!=null&&(n.unstable_strictMode===!0&&(s=!0),n.identifierPrefix!==void 0&&(i=n.identifierPrefix),n.onRecoverableError!==void 0&&(o=n.onRecoverableError)),t=Pc(t,null,e,1,n??null,s,!1,i,o),e[Je]=t.current,qn(e),r)for(e=0;e<r.length;e++)n=r[e],s=n._getVersion,s=s(n._source),t.mutableSourceEagerHydrationData==null?t.mutableSourceEagerHydrationData=[n,s]:t.mutableSourceEagerHydrationData.push(n,s);return new As(t)};Se.render=function(e,t,n){if(!Ps(t))throw Error(S(200));return _s(null,e,t,!1,n)};Se.unmountComponentAtNode=function(e){if(!Ps(e))throw Error(S(40));return e._reactRootContainer?($t(function(){_s(null,null,e,!1,function(){e._reactRootContainer=null,e[Je]=null})}),!0):!1};Se.unstable_batchedUpdates=Zi;Se.unstable_renderSubtreeIntoContainer=function(e,t,n,r){if(!Ps(n))throw Error(S(200));if(e==null||e._reactInternals===void 0)throw Error(S(38));return _s(e,t,n,!1,r)};Se.version="18.3.1-next-f1338f8080-20240426";function zc(){if(!(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__>"u"||typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE!="function"))try{__REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE(zc)}catch(e){console.error(e)}}zc(),za.exports=Se;var wp=za.exports,ga=wp;dl.createRoot=ga.createRoot,dl.hydrateRoot=ga.hydrateRoot;const cl={}.VITE_API_URL;class jp{constructor(t){Ce(this,"baseURL");this.baseURL=t}async request(t,n={}){const r=`${this.baseURL}${t}`,s=new AbortController,i=setTimeout(()=>s.abort(),1e4),o={headers:{"Content-Type":"application/json",...n.headers},signal:s.signal,...n};try{const a=await fetch(r,o);if(clearTimeout(i),!a.ok){const u=await a.json().catch(()=>({}));throw new Error(u.error||`HTTP ${a.status}: ${a.statusText}`)}return await a.json()}catch(a){throw clearTimeout(i),console.error(`API request failed: ${r}`,a),a}}async get(t){return this.request(t,{method:"GET"})}async post(t,n){return this.request(t,{method:"POST",body:n?JSON.stringify(n):void 0})}async put(t,n){return this.request(t,{method:"PUT",body:n?JSON.stringify(n):void 0})}async delete(t){return this.request(t,{method:"DELETE"})}}class Np{constructor(){Ce(this,"client");Ce(this,"isOnline",!0);Ce(this,"connectionPromise",null);this.client=new jp(cl)}async checkConnection(){if(this.connectionPromise)return console.log("[ApiService] Connection check already in progress, reusing promise"),this.connectionPromise;this.connectionPromise=this._performConnectionCheck();try{return await this.connectionPromise}finally{this.connectionPromise=null}}async _performConnectionCheck(){try{if(!cl)return console.warn("[ApiService] VITE_API_URL not configured"),this.isOnline=!1,!1;const n=`${cl.replace("/api","")}/health`;console.log(`[ApiService] Checking connection to: ${n}`);const r=new AbortController;let s=null;try{s=setTimeout(()=>{console.log("[ApiService] Connection check timeout, aborting request"),r.abort()},8e3);const i=await fetch(n,{signal:r.signal,method:"GET",cache:"no-cache",headers:{"Cache-Control":"no-cache"}});return s&&(clearTimeout(s),s=null),console.log(`[ApiService] Response status: ${i.status}`),i.ok?(this.isOnline=!0,console.log("[ApiService] Connection check successful"),!0):(this.isOnline=!1,console.warn(`[ApiService] Connection check failed with status: ${i.status}`),!1)}catch(i){throw s&&clearTimeout(s),i}}catch(t){return console.warn("[ApiService] Backend server is not available:",t),t instanceof Error&&(t.name==="AbortError"?console.warn("[ApiService] Request was aborted (likely due to timeout)"):t.name==="TypeError"&&t.message.includes("fetch")?console.warn("[ApiService] Network error or server not reachable"):console.warn(`[ApiService] Unexpected error: ${t.name} - ${t.message}`)),this.isOnline=!1,!1}}getConnectionStatus(){return this.isOnline}async getLLMConfigs(){return this.client.get("/llm-configs")}async createLLMConfig(t){return this.client.post("/llm-configs",t)}async updateLLMConfig(t,n){return this.client.put(`/llm-configs/${t}`,n)}async deleteLLMConfig(t){try{await this.client.delete(`/llm-configs/${t}`)}catch(n){throw n instanceof Error&&n.message.includes("无法删除正在使用的LLM配置")?new Error("无法删除正在使用的LLM配置，请先从智能体中移除此配置"):n}}async getAgents(){return this.client.get("/agents")}async createAgent(t){return this.client.post("/agents",t)}async updateAgent(t,n){return this.client.put(`/agents/${t}`,n)}async deleteAgent(t){await this.client.delete(`/agents/${t}`)}async getDiscussions(){return this.client.get("/discussions")}async createDiscussion(t){return this.client.post("/discussions",t)}async updateDiscussion(t,n){return this.client.put(`/discussions/${t}`,n)}async deleteDiscussion(t){await this.client.delete(`/discussions/${t}`)}async addMessage(t,n){return this.client.post(`/discussions/${t}/messages`,n)}async getSettings(){return this.client.get("/settings")}async updateSettings(t){return this.client.put("/settings",t)}async getPreferences(){return this.client.get("/preferences")}async updatePreferences(t){return this.client.put("/preferences",t)}async exportData(){return this.client.get("/data/export")}async importData(t,n=!1){await this.client.post("/data/import",{...t,clearExisting:n})}async clearAllData(){await this.client.delete("/data/clear")}async getStorageInfo(){return this.client.get("/storage/info")}}const O=new Np,bt=class bt{constructor(){Ce(this,"isInitialized",!1);Ce(this,"storageMode","server");Ce(this,"serverAvailable",!1)}static getInstance(){return bt.instance||(bt.instance=new bt),bt.instance}async initialize(){if(this.isInitialized)return;console.log("Starting StorageService initialization..."),console.log("Checking server connection..."),console.log(`API_BASE_URL: ${{}.VITE_API_URL}`);let t=0;const n=3;for(;t<n&&!this.serverAvailable;){t++,console.log(`[StorageService] Connection attempt ${t}/${n}`);try{if(this.serverAvailable=await O.checkConnection(),console.log(`[StorageService] Connection check result: ${this.serverAvailable}`),this.serverAvailable)break;t<n&&(console.log("[StorageService] Waiting 2 seconds before retry..."),await new Promise(r=>setTimeout(r,2e3)))}catch(r){console.error(`Server connection check failed (attempt ${t}):`,r),this.serverAvailable=!1,t<n&&(console.log("[StorageService] Waiting 2 seconds before retry..."),await new Promise(s=>setTimeout(s,2e3)))}}if(console.log(`Server available: ${this.serverAvailable}`),!this.serverAvailable)throw new Error("服务器连接失败，请确保后端服务正在运行");await this.determineStorageMode(),console.log(`Storage mode: ${this.storageMode}`),await this.initializeDefaultSettings(),console.log("Default settings initialized"),await this.migrateData(),console.log("Data migration completed"),this.isInitialized=!0,console.log(`StorageService initialized successfully with ${this.storageMode} mode`)}async determineStorageMode(){this.storageMode="server"}setStorageMode(t){this.storageMode=t}getStorageMode(){return this.storageMode}isServerAvailable(){return this.serverAvailable}async refreshServerConnection(){return this.serverAvailable=await O.checkConnection(),this.serverAvailable}async initializeDefaultSettings(){const t={version:"1.0.0",lastUpdated:new Date().toISOString(),autoSave:!0,maxStoredDiscussions:100,defaultDiscussionMode:"free",theme:"light"},n={defaultAgentCount:3,preferredLLMProvider:"openai",autoStartDiscussion:!1,showAdvancedOptions:!1,notificationsEnabled:!0,exportFormat:"json"};this.getSettings()||this.saveSettings(t),this.getPreferences()||this.savePreferences(n)}async migrateData(){const t=await this.getSettings();((t==null?void 0:t.version)||"0.0.0")<"1.0.0"&&console.log("Migrating data to version 1.0.0...")}async saveAgents(t){try{if(this.serverAvailable){console.log(`[StorageService] Starting batch save for ${t.length} agents`);const n=await this.getAgents(),r=t.map(async s=>n.findIndex(o=>o.id===s.id)>=0?O.updateAgent(s.id,s):O.createAgent(s));await Promise.all(r),console.log(`[StorageService] Successfully saved ${t.length} agents to server`)}else throw new Error("服务器不可用，无法保存智能体数据");this.updateLastModified()}catch(n){throw console.error("Failed to save agents:",n),new Error("Failed to save agents to storage")}}async getAgents(){if(this.serverAvailable)return await O.getAgents();throw new Error("服务器不可用，无法获取智能体数据")}async saveAgent(t){if(this.serverAvailable)(await this.getAgents()).findIndex(s=>s.id===t.id)>=0?await O.updateAgent(t.id,t):await O.createAgent(t);else throw new Error("服务器不可用，无法保存智能体")}async deleteAgent(t){if(this.serverAvailable)await O.deleteAgent(t);else throw new Error("服务器不可用，无法删除智能体")}async saveLLMConfigs(t){console.log(`[StorageService] Starting saveLLMConfigs with ${t.length} configs`),console.log(`[StorageService] Storage mode: ${this.storageMode}, Server available: ${this.serverAvailable}`);try{if(this.serverAvailable){console.log("[StorageService] Attempting to save LLM configs to server...");const n=await this.getLLMConfigs(),r=t.map(async s=>n.findIndex(o=>o.id===s.id)>=0?O.updateLLMConfig(s.id,s):O.createLLMConfig(s));await Promise.all(r),console.log(`[StorageService] Successfully saved ${t.length} LLM configs to server`)}else throw new Error("服务器不可用，无法保存LLM配置");this.updateLastModified(),console.log("[StorageService] saveLLMConfigs completed successfully")}catch(n){throw console.error("[StorageService] Failed to save LLM configs:",n),new Error("Failed to save LLM configs to storage")}}async getLLMConfigs(){if(this.serverAvailable){console.log("[StorageService] Using SERVER mode - attempting to load from server");const t=await O.getLLMConfigs();return console.log(`[StorageService] Successfully loaded ${t.length} configs from server`),t}else throw new Error("服务器不可用，无法获取LLM配置数据")}async saveLLMConfig(t){if(console.log(`[StorageService] Starting saveLLMConfig for config: ${t.id} (${t.name})`),console.log(`[StorageService] Storage mode: ${this.storageMode}, Server available: ${this.serverAvailable}`),this.serverAvailable){console.log("[StorageService] Attempting to save single config to server...");const r=(await this.getLLMConfigs()).findIndex(s=>s.id===t.id);console.log(`[StorageService] Existing config index: ${r}`),r>=0?(await O.updateLLMConfig(t.id,t),console.log("[StorageService] Config updated on server successfully")):(await O.createLLMConfig(t),console.log("[StorageService] Config created on server successfully")),console.log("[StorageService] saveLLMConfig completed successfully")}else throw new Error("服务器不可用，无法保存LLM配置")}async deleteLLMConfig(t){if(console.log(`[StorageService] Starting deleteLLMConfig for config: ${t}`),console.log(`[StorageService] Storage mode: ${this.storageMode}, Server available: ${this.serverAvailable}`),this.serverAvailable)console.log("[StorageService] Attempting to delete config from server..."),await O.deleteLLMConfig(t),console.log("[StorageService] Config deleted from server successfully"),console.log("[StorageService] deleteLLMConfig completed successfully");else throw new Error("服务器不可用，无法删除LLM配置")}async exportLLMConfigs(){try{const n=(await this.getLLMConfigs()).map(r=>({...r,apiKey:"***HIDDEN***"}));return JSON.stringify(n,null,2)}catch(t){throw console.error("Failed to export LLM configs:",t),new Error("导出LLM配置失败")}}async importLLMConfigs(t){const n={success:0,errors:[]};try{const r=JSON.parse(t);if(!Array.isArray(r))throw new Error("数据格式错误：应该是配置数组");for(const s of r)try{if(!s.id||!s.name||!s.provider||!s.model){n.errors.push(`配置 ${s.name||"Unknown"} 缺少必要字段`);continue}if(s.apiKey==="***HIDDEN***"){n.errors.push(`配置 ${s.name} 的API密钥需要重新设置`);continue}await this.saveLLMConfig(s),n.success++}catch(i){n.errors.push(`导入配置 ${s.name||"Unknown"} 失败: ${i instanceof Error?i.message:"未知错误"}`)}}catch(r){n.errors.push("解析JSON数据失败: "+(r instanceof Error?r.message:"未知错误"))}return n}generateLLMConfigId(){return`llm_${Date.now()}_${Math.random().toString(36).substring(2,11)}`}createDefaultLLMConfig(t,n,r){return{id:this.generateLLMConfigId(),name:t.name,provider:t.provider.toLowerCase(),model:t.model,apiKey:n,baseURL:r,temperature:t.defaultSettings.temperature,maxTokens:t.defaultSettings.maxTokens}}async getLLMConfig(t){try{return(await this.getLLMConfigs()).find(r=>r.id===t)||null}catch(n){return console.error("Failed to get LLM config:",n),null}}validateLLMConfig(t){var r,s,i,o;const n=[];return(r=t.name)!=null&&r.trim()||n.push("配置名称不能为空"),t.provider||n.push("请选择提供商"),(s=t.model)!=null&&s.trim()||n.push("模型名称不能为空"),(i=t.apiKey)!=null&&i.trim()||n.push("API密钥不能为空"),t.temperature!==void 0&&(t.temperature<0||t.temperature>2)&&n.push("温度值应在0-2之间"),t.maxTokens!==void 0&&(t.maxTokens<1||t.maxTokens>4e3)&&n.push("最大令牌数应在1-4000之间"),t.provider==="azure"&&!((o=t.baseURL)!=null&&o.trim())&&n.push("Azure提供商需要设置基础URL"),n}async getLLMConfigStats(){try{const t=await this.getLLMConfigs(),n={};t.forEach(s=>{n[s.provider]=(n[s.provider]||0)+1});const r=t.slice(0,5);return{total:t.length,byProvider:n,recentlyUsed:r}}catch(t){return console.error("Failed to get LLM config stats:",t),{total:0,byProvider:{},recentlyUsed:[]}}}async saveDiscussions(t){try{const n=await this.getSettings(),r=(n==null?void 0:n.maxStoredDiscussions)||100,s=t.sort((i,o)=>new Date(o.createdAt).getTime()-new Date(i.createdAt).getTime()).slice(0,r);if(this.serverAvailable){console.log(`[StorageService] Starting batch save for ${s.length} discussions`);const i=await this.getDiscussions(),o=s.map(async a=>i.findIndex(p=>p.id===a.id)>=0?O.updateDiscussion(a.id,a):O.createDiscussion(a));await Promise.all(o),console.log(`[StorageService] Successfully saved ${s.length} discussions to server`)}else throw new Error("服务器不可用，无法保存讨论数据");this.updateLastModified()}catch(n){throw console.error("Failed to save discussions:",n),new Error("Failed to save discussions to storage")}}async getDiscussions(){if(this.serverAvailable)return await O.getDiscussions();throw new Error("服务器不可用，无法获取讨论数据")}async saveDiscussion(t){try{if(this.serverAvailable)(await this.getDiscussions()).findIndex(s=>s.id===t.id)>=0?await O.updateDiscussion(t.id,t):await O.createDiscussion(t);else throw new Error("服务器不可用，无法保存讨论")}catch(n){throw console.error("Failed to save discussion:",n),n}}async deleteDiscussion(t){try{if(this.serverAvailable)await O.deleteDiscussion(t);else throw new Error("服务器不可用，无法删除讨论")}catch(n){throw console.error("Failed to delete discussion:",n),n}}async saveSettings(t){if(this.serverAvailable)await O.updateSettings(t);else throw new Error("服务器不可用，无法保存设置")}async getSettings(){if(this.serverAvailable)return await O.getSettings();throw new Error("服务器不可用，无法获取设置数据")}async savePreferences(t){try{if(this.serverAvailable)await O.updatePreferences(t);else throw new Error("服务器不可用，无法保存用户偏好")}catch(n){throw console.error("Failed to save preferences:",n),new Error("Failed to save preferences to storage")}}async getPreferences(){try{if(this.serverAvailable)return await O.getPreferences();throw new Error("服务器不可用，无法获取用户偏好数据")}catch(t){throw console.error("Failed to load preferences:",t),t}}async getAllData(){return{agents:await this.getAgents(),llmConfigs:await this.getLLMConfigs(),discussions:await this.getDiscussions(),settings:await this.getSettings()||{},preferences:await this.getPreferences()||{}}}async importAllData(t){if(this.serverAvailable)await O.importData(t,!1);else throw new Error("服务器不可用，无法导入数据")}async clearAllData(){if(this.serverAvailable)await O.clearAllData();else throw new Error("服务器不可用，无法清除数据")}async updateLastModified(){const t=await this.getSettings();t&&(t.lastUpdated=new Date().toISOString(),await this.saveSettings(t))}validateData(t){try{return!(t.agents&&!Array.isArray(t.agents)||t.llmConfigs&&!Array.isArray(t.llmConfigs)||t.discussions&&!Array.isArray(t.discussions))}catch{return!1}}async getStorageInfo(){if(this.serverAvailable)return await O.getStorageInfo();throw new Error("服务器不可用，无法获取存储信息")}};Ce(bt,"instance");let si=bt;const I=si.getInstance();let Pr;const Sp=new Uint8Array(16);function kp(){if(!Pr&&(Pr=typeof crypto<"u"&&crypto.getRandomValues&&crypto.getRandomValues.bind(crypto),!Pr))throw new Error("crypto.getRandomValues() not supported. See https://github.com/uuidjs/uuid#getrandomvalues-not-supported");return Pr(Sp)}const ne=[];for(let e=0;e<256;++e)ne.push((e+256).toString(16).slice(1));function Cp(e,t=0){return ne[e[t+0]]+ne[e[t+1]]+ne[e[t+2]]+ne[e[t+3]]+"-"+ne[e[t+4]]+ne[e[t+5]]+"-"+ne[e[t+6]]+ne[e[t+7]]+"-"+ne[e[t+8]]+ne[e[t+9]]+"-"+ne[e[t+10]]+ne[e[t+11]]+ne[e[t+12]]+ne[e[t+13]]+ne[e[t+14]]+ne[e[t+15]]}const Ep=typeof crypto<"u"&&crypto.randomUUID&&crypto.randomUUID.bind(crypto),xa={randomUUID:Ep};function li(e,t,n){if(xa.randomUUID&&!t&&!e)return xa.randomUUID();e=e||{};const r=e.random||(e.rng||kp)();if(r[6]=r[6]&15|64,r[8]=r[8]&63|128,t){n=n||0;for(let s=0;s<16;++s)t[n+s]=r[s];return t}return Cp(r)}const Ic={agents:[],currentDiscussion:null,allDiscussions:[],isDiscussionActive:!1,settings:null,preferences:null,isLoading:!0,loadingStep:"storage"},Rc=M.createContext({state:Ic,dispatch:()=>null,addAgent:()=>null,updateAgent:()=>null,deleteAgent:()=>null,startDiscussion:()=>null,endDiscussion:()=>null,sendMessage:()=>null,updateSettings:()=>null,updatePreferences:()=>null,exportData:async()=>"",importData:async()=>!1,clearAllData:async()=>{}});function Lp(e,t){switch(t.type){case"ADD_AGENT":return{...e,agents:[...e.agents,t.payload]};case"UPDATE_AGENT":return{...e,agents:e.agents.map(o=>o.id===t.payload.id?t.payload:o)};case"DELETE_AGENT":return{...e,agents:e.agents.filter(o=>o.id!==t.payload)};case"START_DISCUSSION":const n={id:li(),topic:t.payload.topic,mode:t.payload.mode,participants:t.payload.selectedAgents,messages:[],status:"active",consensus:null,createdAt:new Date,consensusScore:0};return{...e,currentDiscussion:n,isDiscussionActive:!0};case"ADD_MESSAGE":if(!e.currentDiscussion)return e;const r={...e.currentDiscussion,messages:[...e.currentDiscussion.messages,t.payload]};return{...e,currentDiscussion:r};case"UPDATE_CONSENSUS":if(!e.currentDiscussion)return e;const s={...e.currentDiscussion,consensusScore:t.payload.consensusScore,consensus:t.payload.consensus||e.currentDiscussion.consensus,status:t.payload.consensusScore>80?"consensus":e.currentDiscussion.status};return{...e,currentDiscussion:s};case"END_DISCUSSION":if(!e.currentDiscussion)return e;const i={...e.currentDiscussion,status:"ended"};return{...e,currentDiscussion:null,allDiscussions:[i,...e.allDiscussions],isDiscussionActive:!1};case"LOAD_STATE":return t.payload;case"UPDATE_SETTINGS":return{...e,settings:t.payload};case"UPDATE_PREFERENCES":return{...e,preferences:t.payload};case"SET_LOADING":return{...e,isLoading:t.payload};case"SET_LOADING_STEP":return{...e,loadingStep:t.payload};case"SET_ALL_DISCUSSIONS":return{...e,allDiscussions:t.payload};case"INITIALIZE_SUCCESS":return{...e,agents:t.payload.agents,allDiscussions:t.payload.discussions,settings:t.payload.settings,preferences:t.payload.preferences,isLoading:!1,loadingStep:"complete"};default:return e}}function bp({children:e}){const[t,n]=M.useReducer(Lp,Ic);M.useEffect(()=>{let v=!1;return(async()=>{if(v)return;const d=Date.now();try{console.log("Starting app initialization..."),n({type:"SET_LOADING",payload:!0}),n({type:"SET_LOADING_STEP",payload:"storage"}),console.log("Initializing storage service...");const c=Date.now();if(await I.initialize(),v||(console.log(`Storage service initialized in ${Date.now()-c}ms`),n({type:"SET_LOADING_STEP",payload:"server"}),I.isServerAvailable()||console.warn("后端服务器不可用，系统无法正常工作"),v))return;n({type:"SET_LOADING_STEP",payload:"agents"}),console.log("Loading data...");const g=Date.now(),j=await m();n({type:"SET_LOADING_STEP",payload:"discussions"}),console.log(`Data loaded in ${Date.now()-g}ms`),n({type:"INITIALIZE_SUCCESS",payload:j}),console.log(`App initialization completed in ${Date.now()-d}ms`)}catch(c){throw console.error("Failed to initialize app:",c),console.error("Error details:",{message:c instanceof Error?c.message:"Unknown error",stack:c instanceof Error?c.stack:void 0,initTime:Date.now()-d}),c}})(),()=>{v=!0}},[]),M.useEffect(()=>{if(!t.isLoading){const v=setTimeout(async()=>{try{console.log("Auto-saving data...");const N=[];N.push(I.saveAgents(t.agents));const d=t.currentDiscussion?[...t.allDiscussions,t.currentDiscussion]:t.allDiscussions;N.push(I.saveDiscussions(d)),t.settings&&N.push(I.saveSettings(t.settings)),t.preferences&&N.push(I.savePreferences(t.preferences)),await Promise.all(N),console.log("Auto-save completed")}catch(N){console.error("Failed to auto-save data:",N)}},1e3);return()=>clearTimeout(v)}},[t.agents,t.allDiscussions,t.currentDiscussion,t.settings,t.preferences,t.isLoading]);const r=v=>{const N={...v,id:li(),isActive:!0};n({type:"ADD_AGENT",payload:N})},s=v=>{n({type:"UPDATE_AGENT",payload:v})},i=async v=>{try{await I.deleteAgent(v),n({type:"DELETE_AGENT",payload:v})}catch(N){throw console.error("Failed to delete agent:",N),N}},o=v=>{n({type:"START_DISCUSSION",payload:v})},a=async()=>{if(t.currentDiscussion)try{const v={...t.currentDiscussion,status:"ended"};await I.saveDiscussion(v),n({type:"END_DISCUSSION"});const N=await I.getDiscussions();n({type:"SET_ALL_DISCUSSIONS",payload:N})}catch(v){console.error("Failed to save discussion:",v),n({type:"END_DISCUSSION"})}else n({type:"END_DISCUSSION"})},u=async(v,N,d)=>{const c={id:li(),agentId:N,content:v,type:d,timestamp:new Date};if(n({type:"ADD_MESSAGE",payload:c}),t.currentDiscussion)try{await O.addMessage(t.currentDiscussion.id,c)}catch(f){console.error("Failed to save message:",f)}},p=v=>{n({type:"UPDATE_SETTINGS",payload:v})},h=v=>{n({type:"UPDATE_PREFERENCES",payload:v})},m=async()=>{try{const[v,N,d,c]=await Promise.all([I.getAgents(),I.getDiscussions(),I.getSettings(),I.getPreferences()]);return{agents:v||[],discussions:N||[],settings:d||{},preferences:c||{}}}catch(v){throw console.error("Failed to load data:",v),v}},x=async()=>{try{const v=await I.getAllData();return JSON.stringify(v,null,2)}catch(v){throw console.error("Failed to export data:",v),new Error("导出数据失败")}},y=async v=>{try{const N=JSON.parse(v);if(!I.validateData(N))throw new Error("数据格式无效");await I.importAllData(N);const d=await m();return n({type:"INITIALIZE_SUCCESS",payload:d}),!0}catch(N){return console.error("Failed to import data:",N),!1}},w=async()=>{try{await I.clearAllData(),n({type:"INITIALIZE_SUCCESS",payload:{agents:[],discussions:[],settings:{},preferences:{}}})}catch(v){throw console.error("Failed to clear data:",v),new Error("清除数据失败")}};return l.jsx(Rc.Provider,{value:{state:t,dispatch:n,addAgent:r,updateAgent:s,deleteAgent:i,startDiscussion:o,endDiscussion:a,sendMessage:u,updateSettings:p,updatePreferences:h,exportData:x,importData:y,clearAllData:w},children:e})}const St=()=>{const e=M.useContext(Rc);if(!e)throw new Error("useApp must be used within AppProvider");return e};var Mp={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};const Tp=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),Dp=(e,t)=>{const n=M.forwardRef(({color:r="currentColor",size:s=24,strokeWidth:i=2,absoluteStrokeWidth:o,children:a,...u},p)=>M.createElement("svg",{ref:p,...Mp,width:s,height:s,stroke:r,strokeWidth:o?Number(i)*24/Number(s):i,className:`lucide lucide-${Tp(e)}`,...u},[...t.map(([h,m])=>M.createElement(h,m)),...(Array.isArray(a)?a:[a])||[]]));return n.displayName=`${e}`,n};var D=Dp;const hn=D("AlertCircle",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]]),ii=D("AlertTriangle",[["path",{d:"m21.73 18-8-14a2 2 0 0 0-3.48 0l-8 14A2 2 0 0 0 4 21h16a2 2 0 0 0 1.73-3Z",key:"c3ski4"}],["path",{d:"M12 9v4",key:"juzpu7"}],["path",{d:"M12 17h.01",key:"p32p05"}]]),Ap=D("BarChart3",[["path",{d:"M3 3v18h18",key:"1s2lah"}],["path",{d:"M18 17V9",key:"2bz60n"}],["path",{d:"M13 17V5",key:"1frdt8"}],["path",{d:"M8 17v-3",key:"17ska0"}]]),xt=D("Brain",[["path",{d:"M9.5 2A2.5 2.5 0 0 1 12 4.5v15a2.5 2.5 0 0 1-4.96.44 2.5 2.5 0 0 1-2.96-3.08 3 3 0 0 1-.34-5.58 2.5 2.5 0 0 1 1.32-4.24 2.5 2.5 0 0 1 1.98-3A2.5 2.5 0 0 1 9.5 2Z",key:"1mhkh5"}],["path",{d:"M14.5 2A2.5 2.5 0 0 0 12 4.5v15a2.5 2.5 0 0 0 4.96.44 2.5 2.5 0 0 0 2.96-3.08 3 3 0 0 0 .34-5.58 2.5 2.5 0 0 0-1.32-4.24 2.5 2.5 0 0 0-1.98-3A2.5 2.5 0 0 0 14.5 2Z",key:"1d6s00"}]]),Pp=D("Calendar",[["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",ry:"2",key:"eu3xkr"}],["line",{x1:"16",x2:"16",y1:"2",y2:"6",key:"m3sa8f"}],["line",{x1:"8",x2:"8",y1:"2",y2:"6",key:"18kwsl"}],["line",{x1:"3",x2:"21",y1:"10",y2:"10",key:"xt86sb"}]]),gn=D("CheckCircle",[["path",{d:"M22 11.08V12a10 10 0 1 1-5.93-9.14",key:"g774vq"}],["polyline",{points:"22 4 12 14.01 9 11.01",key:"6xbx8j"}]]),_p=D("ChevronDown",[["path",{d:"m6 9 6 6 6-6",key:"qrunsl"}]]),zp=D("ChevronUp",[["path",{d:"m18 15-6-6-6 6",key:"153udz"}]]),Oc=D("Clock",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["polyline",{points:"12 6 12 12 16 14",key:"68esgv"}]]),$c=D("Cpu",[["rect",{x:"4",y:"4",width:"16",height:"16",rx:"2",key:"1vbyd7"}],["rect",{x:"9",y:"9",width:"6",height:"6",key:"o3kz5p"}],["path",{d:"M15 2v2",key:"13l42r"}],["path",{d:"M15 20v2",key:"15mkzm"}],["path",{d:"M2 15h2",key:"1gxd5l"}],["path",{d:"M2 9h2",key:"1bbxkp"}],["path",{d:"M20 15h2",key:"19e6y8"}],["path",{d:"M20 9h2",key:"19tzq7"}],["path",{d:"M9 2v2",key:"165o2o"}],["path",{d:"M9 20v2",key:"i2bqo8"}]]),lo=D("Database",[["ellipse",{cx:"12",cy:"5",rx:"9",ry:"3",key:"msslwz"}],["path",{d:"M3 5V19A9 3 0 0 0 21 19V5",key:"1wlel7"}],["path",{d:"M3 12A9 3 0 0 0 21 12",key:"mv7ke4"}]]),oi=D("Download",[["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}],["polyline",{points:"7 10 12 15 17 10",key:"2ggqvy"}],["line",{x1:"12",x2:"12",y1:"15",y2:"3",key:"1vk2je"}]]),Ip=D("EyeOff",[["path",{d:"M9.88 9.88a3 3 0 1 0 4.24 4.24",key:"1jxqfv"}],["path",{d:"M10.73 5.08A10.43 10.43 0 0 1 12 5c7 0 10 7 10 7a13.16 13.16 0 0 1-1.67 2.68",key:"9wicm4"}],["path",{d:"M6.61 6.61A13.526 13.526 0 0 0 2 12s3 7 10 7a9.74 9.74 0 0 0 5.39-1.61",key:"1jreej"}],["line",{x1:"2",x2:"22",y1:"2",y2:"22",key:"a6p6uj"}]]),Rp=D("Eye",[["path",{d:"M2 12s3-7 10-7 10 7 10 7-3 7-10 7-10-7-10-7Z",key:"rwhkz3"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]]),Op=D("FileText",[["path",{d:"M14.5 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7.5L14.5 2z",key:"1nnpy2"}],["polyline",{points:"14 2 14 8 20 8",key:"1ew0cm"}],["line",{x1:"16",x2:"8",y1:"13",y2:"13",key:"14keom"}],["line",{x1:"16",x2:"8",y1:"17",y2:"17",key:"17nazh"}],["line",{x1:"10",x2:"8",y1:"9",y2:"9",key:"1a5vjj"}]]),$p=D("HardDrive",[["line",{x1:"22",x2:"2",y1:"12",y2:"12",key:"1y58io"}],["path",{d:"M5.45 5.11 2 12v6a2 2 0 0 0 2 2h16a2 2 0 0 0 2-2v-6l-3.45-6.89A2 2 0 0 0 16.76 4H7.24a2 2 0 0 0-1.79 1.11z",key:"oot6mr"}],["line",{x1:"6",x2:"6.01",y1:"16",y2:"16",key:"sgf278"}],["line",{x1:"10",x2:"10.01",y1:"16",y2:"16",key:"1l4acy"}]]),Up=D("HelpCircle",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M9.09 9a3 3 0 0 1 5.83 1c0 2-3 3-3 3",key:"1u773s"}],["path",{d:"M12 17h.01",key:"p32p05"}]]),gs=D("History",[["path",{d:"M3 12a9 9 0 1 0 9-9 9.75 9.75 0 0 0-6.74 2.74L3 8",key:"1357e3"}],["path",{d:"M3 3v5h5",key:"1xhq8a"}],["path",{d:"M12 7v5l4 2",key:"1fdv2h"}]]),Fp=D("Info",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M12 16v-4",key:"1dtifu"}],["path",{d:"M12 8h.01",key:"e9boi3"}]]),ya=D("Loader",[["line",{x1:"12",x2:"12",y1:"2",y2:"6",key:"gza1u7"}],["line",{x1:"12",x2:"12",y1:"18",y2:"22",key:"1qhbu9"}],["line",{x1:"4.93",x2:"7.76",y1:"4.93",y2:"7.76",key:"xae44r"}],["line",{x1:"16.24",x2:"19.07",y1:"16.24",y2:"19.07",key:"bxnmvf"}],["line",{x1:"2",x2:"6",y1:"12",y2:"12",key:"89khin"}],["line",{x1:"18",x2:"22",y1:"12",y2:"12",key:"pb8tfm"}],["line",{x1:"4.93",x2:"7.76",y1:"19.07",y2:"16.24",key:"1uxjnu"}],["line",{x1:"16.24",x2:"19.07",y1:"7.76",y2:"4.93",key:"6duxfx"}]]),va=D("MessageCircle",[["path",{d:"m3 21 1.9-5.7a8.5 8.5 0 1 1 3.8 3.8z",key:"v2veuj"}]]),Xe=D("MessageSquare",[["path",{d:"M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z",key:"1lielz"}]]),Uc=D("PenSquare",[["path",{d:"M11 4H4a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7",key:"1qinfi"}],["path",{d:"M18.5 2.5a2.12 2.12 0 0 1 3 3L12 15l-4 1 1-4Z",key:"w2jsv5"}]]),Vp=D("Play",[["polygon",{points:"5 3 19 12 5 21 5 3",key:"191637"}]]),ai=D("Plus",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]]),Hp=D("RefreshCw",[["path",{d:"M3 12a9 9 0 0 1 9-9 9.75 9.75 0 0 1 6.74 2.74L21 8",key:"v9h5vc"}],["path",{d:"M21 3v5h-5",key:"1q7to0"}],["path",{d:"M21 12a9 9 0 0 1-9 9 9.75 9.75 0 0 1-6.74-2.74L3 16",key:"3uifl3"}],["path",{d:"M8 16H3v5",key:"1cv678"}]]),Bp=D("Save",[["path",{d:"M19 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h11l5 5v11a2 2 0 0 1-2 2z",key:"1owoqh"}],["polyline",{points:"17 21 17 13 7 13 7 21",key:"1md35c"}],["polyline",{points:"7 3 7 8 15 8",key:"8nz8an"}]]),Wp=D("Search",[["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}],["path",{d:"m21 21-4.3-4.3",key:"1qie3q"}]]),jn=D("Settings",[["path",{d:"M12.22 2h-.44a2 2 0 0 0-2 2v.18a2 2 0 0 1-1 1.73l-.43.25a2 2 0 0 1-2 0l-.15-.08a2 2 0 0 0-2.73.73l-.22.38a2 2 0 0 0 .73 2.73l.15.1a2 2 0 0 1 1 1.72v.51a2 2 0 0 1-1 1.74l-.15.09a2 2 0 0 0-.73 2.73l.22.38a2 2 0 0 0 2.73.73l.15-.08a2 2 0 0 1 2 0l.43.25a2 2 0 0 1 1 1.73V20a2 2 0 0 0 2 2h.44a2 2 0 0 0 2-2v-.18a2 2 0 0 1 1-1.73l.43-.25a2 2 0 0 1 2 0l.15.08a2 2 0 0 0 2.73-.73l.22-.39a2 2 0 0 0-.73-2.73l-.15-.08a2 2 0 0 1-1-1.74v-.5a2 2 0 0 1 1-1.74l.15-.09a2 2 0 0 0 .73-2.73l-.22-.38a2 2 0 0 0-2.73-.73l-.15.08a2 2 0 0 1-2 0l-.43-.25a2 2 0 0 1-1-1.73V4a2 2 0 0 0-2-2z",key:"1qme2f"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]]),Qp=D("StopCircle",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["rect",{width:"6",height:"6",x:"9",y:"9",key:"1wrtvo"}]]),Kp=D("TestTube",[["path",{d:"M14.5 2v17.5c0 1.4-1.1 2.5-2.5 2.5h0c-1.4 0-2.5-1.1-2.5-2.5V2",key:"187lwq"}],["path",{d:"M8.5 2h7",key:"csnxdl"}],["path",{d:"M14.5 16h-5",key:"1ox875"}]]),Gp=D("ThumbsDown",[["path",{d:"M17 14V2",key:"8ymqnk"}],["path",{d:"M9 18.12 10 14H4.17a2 2 0 0 1-1.92-2.56l2.33-8A2 2 0 0 1 6.5 2H20a2 2 0 0 1 2 2v8a2 2 0 0 1-2 2h-2.76a2 2 0 0 0-1.79 1.11L12 22h0a3.13 3.13 0 0 1-3-3.88Z",key:"s6e0r"}]]),Yp=D("ThumbsUp",[["path",{d:"M7 10v12",key:"1qc93n"}],["path",{d:"M15 5.88 14 10h5.83a2 2 0 0 1 1.92 2.56l-2.33 8A2 2 0 0 1 17.5 22H4a2 2 0 0 1-2-2v-8a2 2 0 0 1 2-2h2.76a2 2 0 0 0 1.79-1.11L12 2h0a3.13 3.13 0 0 1 3 3.88Z",key:"y3tblf"}]]),ar=D("Trash2",[["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6",key:"4alrt4"}],["path",{d:"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2",key:"v07s0e"}],["line",{x1:"10",x2:"10",y1:"11",y2:"17",key:"1uufr5"}],["line",{x1:"14",x2:"14",y1:"11",y2:"17",key:"xtxkd"}]]),Fc=D("TrendingUp",[["polyline",{points:"22 7 13.5 15.5 8.5 10.5 2 17",key:"126l90"}],["polyline",{points:"16 7 22 7 22 13",key:"kwv8wd"}]]),ui=D("Upload",[["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}],["polyline",{points:"17 8 12 3 7 8",key:"t8dd8p"}],["line",{x1:"12",x2:"12",y1:"3",y2:"15",key:"widbto"}]]),Xp=D("User",[["path",{d:"M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2",key:"975kel"}],["circle",{cx:"12",cy:"7",r:"4",key:"17ys0d"}]]),xn=D("Users",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}],["path",{d:"M22 21v-2a4 4 0 0 0-3-3.87",key:"kshegd"}],["path",{d:"M16 3.13a4 4 0 0 1 0 7.75",key:"1da9ce"}]]),Zp=D("Wrench",[["path",{d:"M14.7 6.3a1 1 0 0 0 0 1.4l1.6 1.6a1 1 0 0 0 1.4 0l3.77-3.77a6 6 0 0 1-7.94 7.94l-6.91 6.91a2.12 2.12 0 0 1-3-3l6.91-6.91a6 6 0 0 1 7.94-7.94l-3.76 3.76z",key:"cbrjhi"}]]),Jp=D("X",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]]),wa=D("Zap",[["polygon",{points:"13 2 3 14 12 14 11 22 21 10 12 10 13 2",key:"45s27k"}]]),qp=[{id:"gpt-4-turbo",name:"GPT-4 Turbo",provider:"OpenAI",model:"gpt-4-turbo-preview",description:"OpenAI最新的GPT-4 Turbo模型，性能强大，适合复杂推理",defaultSettings:{temperature:.7,maxTokens:1e3}},{id:"gpt-4",name:"GPT-4",provider:"OpenAI",model:"gpt-4",description:"OpenAI的旗舰模型，适合需要高质量输出的场景",defaultSettings:{temperature:.7,maxTokens:1e3}},{id:"gpt-3.5-turbo",name:"GPT-3.5 Turbo",provider:"OpenAI",model:"gpt-3.5-turbo",description:"快速且经济的模型，适合大多数对话任务",defaultSettings:{temperature:.7,maxTokens:800}},{id:"claude-3-opus",name:"Claude 3 Opus",provider:"Anthropic",model:"claude-3-opus-20240229",description:"Anthropic最强大的模型，擅长复杂分析和创作",defaultSettings:{temperature:.7,maxTokens:1e3}},{id:"claude-3-sonnet",name:"Claude 3 Sonnet",provider:"Anthropic",model:"claude-3-sonnet-20240229",description:"平衡性能和成本的优秀选择",defaultSettings:{temperature:.7,maxTokens:800}},{id:"claude-3-haiku",name:"Claude 3 Haiku",provider:"Anthropic",model:"claude-3-haiku-20240307",description:"快速响应的轻量级模型，适合简单任务",defaultSettings:{temperature:.7,maxTokens:600}},{id:"gpt-4-azure",name:"Azure GPT-4",provider:"Azure",model:"gpt-4",description:"部署在Azure上的GPT-4模型",defaultSettings:{temperature:.7,maxTokens:1e3}},{id:"gpt-35-turbo-azure",name:"Azure GPT-3.5 Turbo",provider:"Azure",model:"gpt-35-turbo",description:"部署在Azure上的GPT-3.5 Turbo模型",defaultSettings:{temperature:.7,maxTokens:800}}],ja=qp;function xs(e){return{openai:"🤖",anthropic:"🧠",azure:"☁️",custom:"⚙️"}[e.toLowerCase()]||"🔧"}function ys(e){return{openai:"bg-green-100 text-green-800",anthropic:"bg-blue-100 text-blue-800",azure:"bg-purple-100 text-purple-800",custom:"bg-gray-100 text-gray-800"}[e.toLowerCase()]||"bg-gray-100 text-gray-800"}const Na=["/images/agent-alex.jpg","/images/agent-luna.jpg","/images/agent-max.jpg","/images/agent-chen.jpg","/images/agent-sam.jpg","/images/agent-robin.jpg","/images/agent-taylor.jpg","/images/agent-zoe.jpg"],em=["技术","商业","设计","营销","数据分析","产品管理","法律","心理学","教育","医疗"],Vc=[{value:"logical",label:"逻辑型"},{value:"creative",label:"创意型"},{value:"analytical",label:"分析型"},{value:"intuitive",label:"直觉型"},{value:"systematic",label:"系统型"}],tm=[{value:"assertive",label:"果断型"},{value:"collaborative",label:"协作型"},{value:"diplomatic",label:"外交型"},{value:"direct",label:"直接型"},{value:"thoughtful",label:"深思型"}],nm=["数据查询","市场分析","技术调研","用户调研","竞品分析","风险评估","法律咨询","创意生成"];function rm(){const{state:e,addAgent:t,updateAgent:n,deleteAgent:r}=St(),[s,i]=M.useState(!1),[o,a]=M.useState(null),u=async h=>{if(confirm("确定要删除这个智能体吗？"))try{await r(h)}catch(m){alert("删除智能体失败: "+(m instanceof Error?m.message:"未知错误"))}},p=h=>{o?(n({...h,id:o.id,isActive:o.isActive}),a(null)):(t(h),i(!1))};return l.jsx("div",{className:"h-full bg-gradient-to-br from-slate-50 to-blue-50 w-full overflow-y-auto",children:l.jsx("div",{className:"centered-container",children:l.jsxs("div",{className:"centered-content",children:[l.jsxs("div",{className:"flex items-center justify-between mb-8",children:[l.jsxs("div",{children:[l.jsx("h1",{className:"text-3xl font-bold text-gray-900 mb-2",children:"智能体管理"}),l.jsx("p",{className:"text-gray-600",children:"配置和管理您的AI智能体团队"})]}),l.jsxs("button",{onClick:()=>i(!0),className:"flex items-center gap-2 bg-blue-600 text-white px-6 py-3 rounded-lg hover:bg-blue-700 transition-colors shadow-lg",children:[l.jsx(ai,{size:20}),"添加智能体"]})]}),l.jsx("div",{className:"flex flex-wrap gap-6 mb-8",children:e.agents.map(h=>l.jsx(sm,{agent:h,onEdit:()=>a(h),onDelete:()=>u(h.id)},h.id))}),(s||o)&&l.jsx(lm,{agent:o,onSubmit:p,onCancel:()=>{i(!1),a(null)}})]})})})}function sm({agent:e,onEdit:t,onDelete:n}){var r;return l.jsxs("div",{className:"bg-white rounded-xl shadow-lg hover:shadow-xl transition-shadow p-6 border border-gray-100 fixed-size-card",children:[l.jsxs("div",{className:"flex items-center gap-4 mb-4",children:[l.jsx("img",{src:e.avatar,alt:e.name,className:"w-16 h-16 rounded-full object-cover border-4 border-blue-100"}),l.jsxs("div",{children:[l.jsx("h3",{className:"text-xl font-semibold text-gray-900",children:e.name}),l.jsx("span",{className:`inline-block px-2 py-1 rounded-full text-xs font-medium ${e.isActive?"bg-green-100 text-green-800":"bg-gray-100 text-gray-600"}`,children:e.isActive?"活跃":"非活跃"})]})]}),l.jsxs("div",{className:"space-y-3 mb-4",children:[l.jsxs("div",{className:"flex items-center gap-2",children:[l.jsx(Xp,{size:16,className:"text-blue-600"}),l.jsx("span",{className:"text-sm text-gray-600",children:"专业领域："}),l.jsxs("div",{className:"flex flex-wrap gap-1",children:[e.expertise.slice(0,2).map((s,i)=>l.jsx("span",{className:"bg-blue-100 text-blue-800 text-xs px-2 py-1 rounded",children:s},i)),e.expertise.length>2&&l.jsxs("span",{className:"text-xs text-gray-500",children:["+",e.expertise.length-2]})]})]}),l.jsxs("div",{className:"flex items-center gap-2",children:[l.jsx(xt,{size:16,className:"text-purple-600"}),l.jsx("span",{className:"text-sm text-gray-600",children:"思维方式："}),l.jsx("span",{className:"text-sm font-medium text-gray-900",children:(r=Vc.find(s=>s.value===e.thinkingStyle))==null?void 0:r.label})]}),l.jsxs("div",{className:"flex items-center gap-2",children:[l.jsx(Zp,{size:16,className:"text-green-600"}),l.jsx("span",{className:"text-sm text-gray-600",children:"工具："}),l.jsxs("span",{className:"text-sm text-gray-500",children:[e.tools.length," 个"]})]}),e.llmConfig&&l.jsxs("div",{className:"flex items-center gap-2",children:[l.jsx(jn,{size:16,className:"text-orange-600"}),l.jsx("span",{className:"text-sm text-gray-600",children:"LLM："}),l.jsxs("div",{className:"flex items-center gap-1",children:[l.jsx("span",{className:"text-sm",children:xs(e.llmConfig.provider)}),l.jsx("span",{className:`text-xs px-2 py-0.5 rounded ${ys(e.llmConfig.provider)}`,children:e.llmConfig.name})]})]})]}),l.jsxs("div",{className:"flex gap-2",children:[l.jsxs("button",{onClick:t,className:"flex-1 bg-blue-50 text-blue-600 py-2 rounded-lg hover:bg-blue-100 transition-colors flex items-center justify-center gap-1",children:[l.jsx(Uc,{size:16}),"编辑"]}),l.jsxs("button",{onClick:n,className:"flex-1 bg-red-50 text-red-600 py-2 rounded-lg hover:bg-red-100 transition-colors flex items-center justify-center gap-1",children:[l.jsx(ar,{size:16}),"删除"]})]})]})}function lm({agent:e,onSubmit:t,onCancel:n}){var x;const[r,s]=M.useState({name:(e==null?void 0:e.name)||"",avatar:(e==null?void 0:e.avatar)||Na[0],expertise:(e==null?void 0:e.expertise)||[],thinkingStyle:(e==null?void 0:e.thinkingStyle)||"logical",personality:(e==null?void 0:e.personality)||"collaborative",tools:(e==null?void 0:e.tools)||[],llmConfig:(e==null?void 0:e.llmConfig)||null}),[i,o]=M.useState([]),[a,u]=M.useState(!0);M.useEffect(()=>{(async()=>{try{u(!0);const w=await I.getLLMConfigs();o(w)}catch(w){console.error("Failed to load LLM configs:",w),o([])}finally{u(!1)}})()},[]);const p=y=>{y.preventDefault(),r.name&&r.expertise.length>0&&r.llmConfig&&t({...r,llmConfig:r.llmConfig})},h=y=>{s(w=>({...w,expertise:w.expertise.includes(y)?w.expertise.filter(v=>v!==y):[...w.expertise,y]}))},m=y=>{s(w=>({...w,tools:w.tools.includes(y)?w.tools.filter(v=>v!==y):[...w.tools,y]}))};return l.jsx("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50",children:l.jsxs("div",{className:"bg-white rounded-xl shadow-2xl max-w-2xl w-full max-h-[90vh] overflow-y-auto",children:[l.jsx("div",{className:"p-6 border-b border-gray-200",children:l.jsx("h2",{className:"text-2xl font-bold text-gray-900",children:e?"编辑智能体":"添加新智能体"})}),l.jsxs("form",{onSubmit:p,className:"p-6 space-y-6",children:[l.jsxs("div",{children:[l.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"智能体名称"}),l.jsx("input",{type:"text",value:r.name,onChange:y=>s(w=>({...w,name:y.target.value})),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent",placeholder:"输入智能体名称",required:!0})]}),l.jsxs("div",{children:[l.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"选择头像"}),l.jsx("div",{className:"grid grid-cols-4 gap-3",children:Na.map((y,w)=>l.jsx("button",{type:"button",onClick:()=>s(v=>({...v,avatar:y})),className:`relative rounded-lg overflow-hidden border-4 transition-all ${r.avatar===y?"border-blue-500 ring-2 ring-blue-200":"border-gray-200 hover:border-gray-300"}`,children:l.jsx("img",{src:y,alt:`Avatar ${w+1}`,className:"w-16 h-16 object-cover"})},w))})]}),l.jsxs("div",{children:[l.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"专业领域（至少选择一个）"}),l.jsx("div",{className:"grid grid-cols-2 gap-2",children:em.map(y=>l.jsx("button",{type:"button",onClick:()=>h(y),className:`px-3 py-2 rounded-lg text-sm transition-colors ${r.expertise.includes(y)?"bg-blue-100 text-blue-800 border-2 border-blue-300":"bg-gray-100 text-gray-700 border-2 border-gray-300 hover:bg-gray-200"}`,children:y},y))})]}),l.jsxs("div",{className:"grid grid-cols-2 gap-4",children:[l.jsxs("div",{children:[l.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"思维方式"}),l.jsx("select",{value:r.thinkingStyle,onChange:y=>s(w=>({...w,thinkingStyle:y.target.value})),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent",children:Vc.map(y=>l.jsx("option",{value:y.value,children:y.label},y.value))})]}),l.jsxs("div",{children:[l.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"性格特征"}),l.jsx("select",{value:r.personality,onChange:y=>s(w=>({...w,personality:y.target.value})),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent",children:tm.map(y=>l.jsx("option",{value:y.value,children:y.label},y.value))})]})]}),l.jsxs("div",{children:[l.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"可用工具"}),l.jsx("div",{className:"grid grid-cols-2 gap-2",children:nm.map(y=>l.jsx("button",{type:"button",onClick:()=>m(y),className:`px-3 py-2 rounded-lg text-sm transition-colors ${r.tools.includes(y)?"bg-green-100 text-green-800 border-2 border-green-300":"bg-gray-100 text-gray-700 border-2 border-gray-300 hover:bg-gray-200"}`,children:y},y))})]}),l.jsxs("div",{children:[l.jsxs("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:["LLM配置 ",l.jsx("span",{className:"text-red-500",children:"*"})]}),a?l.jsxs("div",{className:"w-full px-3 py-2 border border-gray-300 rounded-lg bg-gray-50 flex items-center gap-2",children:[l.jsx("div",{className:"animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600"}),l.jsx("span",{className:"text-gray-600",children:"正在加载LLM配置..."})]}):l.jsxs("select",{value:((x=r.llmConfig)==null?void 0:x.id)||"",onChange:y=>{const w=i.find(v=>v.id===y.target.value);s(v=>({...v,llmConfig:w||null}))},className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent",required:!0,children:[l.jsx("option",{value:"",children:"请选择LLM配置"}),i.map(y=>l.jsxs("option",{value:y.id,children:[xs(y.provider)," ",y.name," (",y.provider.toUpperCase(),")"]},y.id))]}),r.llmConfig&&l.jsxs("div",{className:"mt-2 p-3 bg-gray-50 rounded-lg",children:[l.jsxs("div",{className:"flex items-center gap-2 mb-1",children:[l.jsx("span",{className:"text-sm font-medium text-gray-700",children:"已选择:"}),l.jsx("span",{className:`text-xs px-2 py-1 rounded ${ys(r.llmConfig.provider)}`,children:r.llmConfig.name})]}),l.jsxs("div",{className:"text-xs text-gray-600",children:["模型: ",r.llmConfig.model," | 温度: ",r.llmConfig.temperature," | 令牌: ",r.llmConfig.maxTokens]})]}),i.length===0&&l.jsx("div",{className:"mt-2 p-3 bg-yellow-50 border border-yellow-200 rounded-lg",children:l.jsxs("p",{className:"text-sm text-yellow-800",children:["暂无可用的LLM配置。请先前往",l.jsx("span",{className:"font-medium",children:"LLM配置"}),"页面创建LLM配置，然后再创建智能体。"]})})]}),l.jsxs("div",{className:"flex gap-3 pt-4 border-t border-gray-200",children:[l.jsx("button",{type:"button",onClick:n,className:"flex-1 px-4 py-3 bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200 transition-colors",children:"取消"}),l.jsx("button",{type:"submit",disabled:a||i.length===0,className:"flex-1 px-4 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:bg-gray-400 disabled:cursor-not-allowed transition-colors",children:a?"加载中...":`${e?"更新":"创建"}智能体`})]})]})]})})}function im(){const{state:e,startDiscussion:t}=St(),[n,r]=M.useState({topic:"",mode:"free",selectedAgents:[]}),[s,i]=M.useState([]),o=()=>{const h=[];return n.topic.trim()||h.push("请输入讨论话题"),n.selectedAgents.length<2&&h.push("至少需要选择2个智能体参与讨论"),n.selectedAgents.length>8&&h.push("最多支持8个智能体同时讨论"),i(h),h.length===0},a=()=>{o()&&t(n)},u=h=>{r(m=>({...m,selectedAgents:m.selectedAgents.includes(h)?m.selectedAgents.filter(x=>x!==h):[...m.selectedAgents,h]}))},p=e.agents.filter(h=>h.isActive);return p.length===0?l.jsx("div",{className:"h-full bg-gradient-to-br from-orange-50 to-red-50 overflow-y-auto",children:l.jsx("div",{className:"max-w-4xl mx-auto p-6",children:l.jsxs("div",{className:"bg-white rounded-xl shadow-lg p-8 text-center",children:[l.jsx(hn,{size:64,className:"text-orange-500 mx-auto mb-4"}),l.jsx("h2",{className:"text-2xl font-bold text-gray-900 mb-4",children:"没有可用的智能体"}),l.jsx("p",{className:"text-gray-600 mb-6",children:"您需要先创建和配置智能体才能开始讨论。"}),l.jsx("button",{onClick:()=>window.location.reload(),className:"bg-blue-600 text-white px-6 py-3 rounded-lg hover:bg-blue-700 transition-colors",children:"前往智能体管理"})]})})}):l.jsx("div",{className:"h-full bg-gradient-to-br from-purple-50 to-pink-50 w-full overflow-y-auto",children:l.jsx("div",{className:"flex justify-center p-6",children:l.jsxs("div",{className:"bg-white rounded-xl shadow-lg overflow-hidden",style:{width:"800px",maxWidth:"800px"},children:[l.jsxs("div",{className:"bg-gradient-to-r from-purple-600 to-pink-600 text-white p-8",children:[l.jsxs("div",{className:"flex items-center gap-3 mb-4",children:[l.jsx(va,{size:32}),l.jsx("h1",{className:"text-3xl font-bold",children:"创建新讨论"})]}),l.jsx("p",{className:"text-purple-100",children:"配置讨论话题、模式和参与者，开始智能体之间的协作讨论"})]}),l.jsxs("div",{className:"p-8 space-y-8",children:[s.length>0&&l.jsxs("div",{className:"bg-red-50 border border-red-200 rounded-lg p-4",children:[l.jsxs("div",{className:"flex items-center gap-2 mb-2",children:[l.jsx(hn,{size:20,className:"text-red-600"}),l.jsx("h3",{className:"font-medium text-red-800",children:"配置错误"})]}),l.jsx("ul",{className:"text-red-700 text-sm space-y-1",children:s.map((h,m)=>l.jsxs("li",{children:["• ",h]},m))})]}),l.jsxs("div",{className:"space-y-3",children:[l.jsx("label",{className:"block text-lg font-semibold text-gray-900",children:"讨论话题"}),l.jsx("textarea",{value:n.topic,onChange:h=>r(m=>({...m,topic:h.target.value})),placeholder:"请输入您想要讨论的话题，例如：如何提升用户体验设计质量？",className:"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent resize-none",rows:3}),l.jsx("p",{className:"text-sm text-gray-500",children:"清晰的话题描述有助于智能体更好地理解和参与讨论"})]}),l.jsxs("div",{className:"space-y-4",children:[l.jsx("label",{className:"block text-lg font-semibold text-gray-900",children:"讨论模式"}),l.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[l.jsxs("button",{onClick:()=>r(h=>({...h,mode:"free"})),className:`p-6 rounded-xl border-2 transition-all text-left ${n.mode==="free"?"border-purple-500 bg-purple-50":"border-gray-200 hover:border-gray-300"}`,children:[l.jsxs("div",{className:"flex items-center gap-3 mb-3",children:[l.jsx(va,{size:24,className:n.mode==="free"?"text-purple-600":"text-gray-600"}),l.jsx("h3",{className:"font-semibold text-gray-900",children:"自由讨论模式"})]}),l.jsx("p",{className:"text-gray-600 text-sm",children:"智能体根据话题相关性和兴趣自主发言，讨论更加自然流畅"})]}),l.jsxs("button",{onClick:()=>r(h=>({...h,mode:"moderator"})),className:`p-6 rounded-xl border-2 transition-all text-left ${n.mode==="moderator"?"border-purple-500 bg-purple-50":"border-gray-200 hover:border-gray-300"}`,children:[l.jsxs("div",{className:"flex items-center gap-3 mb-3",children:[l.jsx(jn,{size:24,className:n.mode==="moderator"?"text-purple-600":"text-gray-600"}),l.jsx("h3",{className:"font-semibold text-gray-900",children:"主持人模式"})]}),l.jsx("p",{className:"text-gray-600 text-sm",children:"选择一个智能体作为主持人，按轮次组织讨论，更加有序规范"})]})]})]}),l.jsxs("div",{className:"space-y-4",children:[l.jsxs("div",{className:"flex items-center gap-3",children:[l.jsx(xn,{size:24,className:"text-purple-600"}),l.jsxs("h2",{className:"text-lg font-semibold text-gray-900",children:["选择参与者 (",n.selectedAgents.length,"/8)"]})]}),l.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4",children:p.map(h=>l.jsxs("button",{onClick:()=>u(h.id),className:`p-4 rounded-xl border-2 transition-all text-left ${n.selectedAgents.includes(h.id)?"border-purple-500 bg-purple-50":"border-gray-200 hover:border-gray-300"}`,children:[l.jsxs("div",{className:"flex items-center gap-3 mb-2",children:[l.jsx("img",{src:h.avatar,alt:h.name,className:"w-10 h-10 rounded-full object-cover"}),l.jsxs("div",{children:[l.jsx("h3",{className:"font-medium text-gray-900",children:h.name}),l.jsx("p",{className:"text-sm text-gray-500",children:h.expertise.slice(0,2).join("、")})]})]}),l.jsxs("div",{className:"flex items-center justify-between",children:[l.jsxs("span",{className:"text-xs bg-gray-100 text-gray-600 px-2 py-1 rounded",children:[h.thinkingStyle==="logical"&&"逻辑型",h.thinkingStyle==="creative"&&"创意型",h.thinkingStyle==="analytical"&&"分析型",h.thinkingStyle==="intuitive"&&"直觉型",h.thinkingStyle==="systematic"&&"系统型"]}),n.selectedAgents.includes(h.id)&&l.jsx("div",{className:"w-5 h-5 bg-purple-500 rounded-full flex items-center justify-center",children:l.jsx("div",{className:"w-2 h-2 bg-white rounded-full"})})]})]},h.id))}),l.jsx("p",{className:"text-sm text-gray-500",children:"建议选择具有不同专业背景和思维方式的智能体，以获得更丰富的讨论视角"})]}),l.jsxs("div",{className:"border-t border-gray-200 pt-6",children:[l.jsx("h3",{className:"text-lg font-semibold text-gray-900 mb-4",children:"高级设置（可选）"}),l.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[l.jsxs("div",{children:[l.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"最大消息数量"}),l.jsx("input",{type:"number",min:"10",max:"100",value:n.maxMessages||"",onChange:h=>r(m=>({...m,maxMessages:h.target.value?parseInt(h.target.value):void 0})),placeholder:"不限制",className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent"})]}),l.jsxs("div",{children:[l.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"时间限制（分钟）"}),l.jsx("input",{type:"number",min:"5",max:"120",value:n.timeLimit||"",onChange:h=>r(m=>({...m,timeLimit:h.target.value?parseInt(h.target.value):void 0})),placeholder:"不限制",className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent"})]})]})]}),l.jsx("div",{className:"flex justify-center pt-6",children:l.jsxs("button",{onClick:a,disabled:!n.topic.trim()||n.selectedAgents.length<2,className:"flex items-center gap-3 bg-gradient-to-r from-purple-600 to-pink-600 text-white px-8 py-4 rounded-xl hover:from-purple-700 hover:to-pink-700 disabled:from-gray-400 disabled:to-gray-500 disabled:cursor-not-allowed transition-all shadow-lg text-lg font-medium",children:[l.jsx(Vp,{size:24}),"开始讨论"]})})]})]})})})}const Mt=class Mt{static getInstance(){return Mt.instance||(Mt.instance=new Mt),Mt.instance}async callLLM(t,n){try{return await this.makeAPICall(t,n)}catch(r){throw console.error("LLM API调用失败:",r),new Error(`LLM调用失败: ${r instanceof Error?r.message:"未知错误"}`)}}async generateAgentMessage(t,n,r,s=[]){const i=this.buildSystemPrompt(t,r),o=this.buildConversationHistory(s,t.id),a={messages:[{role:"system",content:i},...o,{role:"user",content:`请基于当前讨论情况，就"${r}"这个话题发表你的观点。`}],temperature:t.llmConfig.temperature||.7,maxTokens:t.llmConfig.maxTokens||500,model:t.llmConfig.model};return(await this.callLLM(t.llmConfig,a)).content}buildSystemPrompt(t,n){return`你是一个名为"${t.name}"的智能体，正在参与关于"${n}"的讨论。

你的特征：
- 专业领域：${t.expertise.join("、")}
- 思维方式：${t.thinkingStyle}
- 性格特征：${t.personality}
- 可用工具：${t.tools.join("、")}

请根据你的专业背景和性格特征参与讨论，保持角色一致性。你的回复应该：
1. 体现你的专业知识和思维方式
2. 符合你的性格特征
3. 简洁明了，通常在100-200字之间
4. 针对讨论话题提供有价值的观点

${t.llmConfig.systemPrompt||""}`}buildConversationHistory(t,n){return t.slice(-10).map(s=>({role:s.agentId===n?"assistant":"user",content:`${s.agentId===n?"我":"其他参与者"}：${s.content}`}))}async makeAPICall(t,n){const r=this.getAPIUrl(t),s=this.getAPIHeaders(t),i=this.formatRequestBody(t,n),o=await fetch(r,{method:"POST",headers:s,body:JSON.stringify(i)});if(!o.ok)throw new Error(`API请求失败: ${o.status} ${o.statusText}`);const a=await o.json();return this.parseResponse(t,a)}getAPIUrl(t){if(t.baseURL)return`${t.baseURL}/chat/completions`;switch(t.provider){case"openai":return"https://api.openai.com/v1/chat/completions";case"anthropic":return"https://api.anthropic.com/v1/messages";case"azure":return`${t.baseURL}/openai/deployments/${t.model}/chat/completions?api-version=2023-12-01-preview`;default:throw new Error(`不支持的提供商: ${t.provider}`)}}getAPIHeaders(t){const n={"Content-Type":"application/json"};switch(t.provider){case"openai":case"azure":case"custom":n.Authorization=`Bearer ${t.apiKey}`;break;case"anthropic":n["x-api-key"]=t.apiKey,n["anthropic-version"]="2023-06-01";break}return n}formatRequestBody(t,n){var r;switch(t.provider){case"anthropic":return{model:n.model||t.model,max_tokens:n.maxTokens||1e3,temperature:n.temperature||.7,messages:n.messages.filter(s=>s.role!=="system"),system:((r=n.messages.find(s=>s.role==="system"))==null?void 0:r.content)||""};default:return{model:n.model||t.model,messages:n.messages,temperature:n.temperature||.7,max_tokens:n.maxTokens||1e3}}}parseResponse(t,n){var r,s,i;switch(t.provider){case"anthropic":return{content:((r=n.content[0])==null?void 0:r.text)||"",usage:n.usage?{promptTokens:n.usage.input_tokens,completionTokens:n.usage.output_tokens,totalTokens:n.usage.input_tokens+n.usage.output_tokens}:void 0,model:n.model};default:return{content:((i=(s=n.choices[0])==null?void 0:s.message)==null?void 0:i.content)||"",usage:n.usage?{promptTokens:n.usage.prompt_tokens,completionTokens:n.usage.completion_tokens,totalTokens:n.usage.total_tokens}:void 0,model:n.model}}}async testLLMConfig(t){try{const n={messages:[{role:"user",content:'请回复"测试成功"'}],temperature:.1,maxTokens:10};return await this.callLLM(t,n),!0}catch(n){return console.error("LLM配置测试失败:",n),!1}}};Ce(Mt,"instance");let ci=Mt;const Hc=ci.getInstance();async function om(e,t,n,r=[]){if(!e.llmConfig)throw new Error(`智能体 ${e.name} 未配置LLM，无法生成消息`);try{return await Hc.generateAgentMessage(e,t,n,r)}catch(s){throw console.error(`智能体 ${e.name} 的LLM调用失败:`,s),new Error(`智能体 ${e.name} 的LLM调用失败: ${s instanceof Error?s.message:"未知错误"}`)}}function am(e,t){if(e.length<3)return 0;const n=e.slice(-10),r=n.filter(m=>m.type==="agreement").length/n.length,s=n.filter(m=>m.type==="disagreement").length/n.length,i=n.filter(m=>m.type==="question").length/n.length,o=new Map;n.forEach(m=>{o.set(m.agentId,(o.get(m.agentId)||0)+1)});const a=o.size,u=t.filter(m=>m.isActive).length,p=a/u;let h=0;return h+=r*40,h+=(1-s)*30,h+=i>.1&&i<.3?15:0,h+=p*15,Math.min(100,Math.max(0,h))}function um(e,t){const r=e.slice(-15).filter(i=>i.type==="statement"||i.type==="agreement");if(r.length===0)return`关于"${t}"，参与者需要更多时间来达成共识。`;const s=cm(r.map(i=>i.content));return`经过充分讨论，大家就"${t}"达成了共识：${s.slice(0,3).join("、")}是关键要素，需要重点关注和实施。`}function cm(e){return["技术创新","用户体验","市场需求","成本控制","时间规划","质量保证","团队合作","数据分析"].filter(()=>Math.random()>.6).slice(0,5)}function dm(e,t,n){if(e.length===0)return null;if(n==="moderator"){const r=t.slice(-e.length).map(i=>i.agentId),s=e.filter(i=>!r.includes(i));return s.length>0?s[0]:e[0]}else{const r=t.slice(-10),s=new Map;e.forEach(u=>s.set(u,0)),r.forEach(u=>{e.includes(u.agentId)&&s.set(u.agentId,(s.get(u.agentId)||0)+1)});const o=[...s.entries()].sort(([,u],[,p])=>u-p).slice(0,Math.ceil(e.length/2));return o[Math.floor(Math.random()*o.length)][0]}}function fm(){const{state:e,dispatch:t,endDiscussion:n,sendMessage:r}=St(),[s,i]=M.useState(!1),[o,a]=M.useState({totalMessages:0,consensusScore:0,activeTime:0}),u=M.useRef(null),p=M.useRef(null),h=M.useRef(null),{currentDiscussion:m}=e;M.useEffect(()=>(m&&m.status==="active"&&(i(!0),x()),()=>{p.current&&clearInterval(p.current),h.current&&clearTimeout(h.current)}),[m]),M.useEffect(()=>{var d;(d=u.current)==null||d.scrollIntoView({behavior:"smooth"})},[m==null?void 0:m.messages]),M.useEffect(()=>{if(m){const d=am(m.messages,e.agents);if(a({totalMessages:m.messages.length,consensusScore:d,activeTime:Math.floor((Date.now()-new Date(m.createdAt).getTime())/1e3)}),t({type:"UPDATE_CONSENSUS",payload:{consensusScore:d}}),d>80&&m.status==="active"){const c=um(m.messages,m.topic);t({type:"UPDATE_CONSENSUS",payload:{consensusScore:d,consensus:c}}),i(!1)}}},[m==null?void 0:m.messages]);const x=()=>{if(!m||m.status!=="active")return;const d=m.participants,c=e.agents.filter(j=>d.includes(j.id)),f=async()=>{if(!m||m.status!=="active"){i(!1);return}const j=dm(d,m.messages,m.mode);if(j){const L=c.find(b=>b.id===j);if(L)try{const b=await om(L,m,m.topic,m.messages.slice(-5)),$=y(b);r(b,L.id,$)}catch(b){console.error("生成消息失败:",b)}}const k=Math.random()*3e3+2e3;h.current=setTimeout(f,k)},g=Math.random()*2e3+1e3;h.current=setTimeout(f,g)},y=d=>d.includes("我赞同")||d.includes("我同意")||d.includes("这个想法很好")?"agreement":d.includes("但是")||d.includes("我认为")||d.includes("不同的看法")?"disagreement":d.includes("？")||d.includes("我们")||d.includes("如何")?"question":"statement",w=()=>{i(!1),h.current&&clearTimeout(h.current),n()},v=d=>{const c=Math.floor(d/60),f=d%60;return`${c}:${f.toString().padStart(2,"0")}`};if(!m)return l.jsx("div",{className:"h-full bg-gradient-to-br from-gray-50 to-blue-50 w-full overflow-y-auto",children:l.jsx("div",{className:"discussion-container",children:l.jsxs("div",{className:"bg-white rounded-xl shadow-lg p-8 text-center max-w-2xl",children:[l.jsx(Xe,{size:64,className:"text-gray-400 mx-auto mb-4"}),l.jsx("h2",{className:"text-2xl font-bold text-gray-900 mb-4",children:"没有进行中的讨论"}),l.jsx("p",{className:"text-gray-600",children:"请先创建一个新的讨论来开始智能体对话。"})]})})});const N=e.agents.filter(d=>m.participants.includes(d.id));return l.jsx("div",{className:"h-screen bg-gradient-to-br from-blue-50 to-indigo-50 w-full overflow-hidden pt-16",children:l.jsx("div",{className:"discussion-container h-full",children:l.jsxs("div",{className:"discussion-content h-full",children:[l.jsx("div",{className:"fixed-size-discussion flex-shrink-0 h-[70vh]",children:l.jsxs("div",{className:"bg-white rounded-xl shadow-lg overflow-hidden h-full flex flex-col",children:[l.jsxs("div",{className:"bg-gradient-to-r from-blue-600 to-indigo-600 text-white p-6",children:[l.jsxs("div",{className:"flex items-center justify-between mb-4",children:[l.jsxs("div",{className:"flex items-center gap-3",children:[l.jsx(Xe,{size:32}),l.jsxs("div",{children:[l.jsx("h1",{className:"text-2xl font-bold",children:"讨论进行中"}),l.jsx("p",{className:"text-blue-100",children:m.mode==="free"?"自由讨论模式":"主持人模式"})]})]}),l.jsxs("div",{className:"flex items-center gap-4",children:[l.jsxs("div",{className:"text-center",children:[l.jsx("div",{className:"text-2xl font-bold",children:v(o.activeTime)}),l.jsx("div",{className:"text-sm text-blue-100",children:"讨论时长"})]}),l.jsxs("button",{onClick:w,className:"flex items-center gap-2 bg-red-500 hover:bg-red-600 px-4 py-2 rounded-lg transition-colors",children:[l.jsx(Qp,{size:20}),"结束讨论"]})]})]}),l.jsxs("div",{className:"bg-white bg-opacity-20 rounded-lg p-4",children:[l.jsx("h3",{className:"font-semibold mb-2",children:"讨论话题"}),l.jsx("p",{className:"text-blue-50",children:m.topic})]})]}),l.jsxs("div",{className:"h-[calc(100%-180px)] overflow-y-auto p-6 space-y-4",children:[m.messages.map(d=>l.jsx(pm,{message:d,agent:N.find(c=>c.id===d.agentId)},d.id)),s&&l.jsxs("div",{className:"flex items-center gap-3 text-gray-500",children:[l.jsx("div",{className:"animate-spin w-5 h-5 border-2 border-blue-500 border-t-transparent rounded-full"}),l.jsx("span",{children:"智能体正在思考中..."})]}),l.jsx("div",{ref:u})]})]})}),l.jsxs("div",{className:"space-y-6 fixed-size-sidebar",children:[l.jsxs("div",{className:"bg-white rounded-xl shadow-lg p-6",children:[l.jsxs("div",{className:"flex items-center gap-3 mb-4",children:[l.jsx(Fc,{size:24,className:"text-green-600"}),l.jsx("h3",{className:"font-semibold text-gray-900",children:"共识度"})]}),l.jsxs("div",{className:"space-y-4",children:[l.jsxs("div",{className:"relative",children:[l.jsx("div",{className:"w-full h-4 bg-gray-200 rounded-full overflow-hidden",children:l.jsx("div",{className:`h-full transition-all duration-500 ${o.consensusScore>80?"bg-green-500":o.consensusScore>60?"bg-yellow-500":"bg-red-500"}`,style:{width:`${o.consensusScore}%`}})}),l.jsxs("div",{className:"text-center mt-2 font-bold text-2xl",children:[Math.round(o.consensusScore),"%"]})]}),m.status==="consensus"&&l.jsxs("div",{className:"flex items-center gap-2 bg-green-50 text-green-800 p-3 rounded-lg",children:[l.jsx(gn,{size:20}),l.jsx("span",{className:"font-medium",children:"已达成共识！"})]})]})]}),l.jsxs("div",{className:"bg-white rounded-xl shadow-lg p-6",children:[l.jsxs("div",{className:"flex items-center gap-3 mb-4",children:[l.jsx(xn,{size:24,className:"text-blue-600"}),l.jsx("h3",{className:"font-semibold text-gray-900",children:"参与者"})]}),l.jsx("div",{className:"space-y-3",children:N.map(d=>{const c=m.messages.filter(g=>g.agentId===d.id).length,f=m.messages.slice().reverse().find(g=>g.agentId===d.id);return l.jsxs("div",{className:"flex items-center gap-3 p-3 bg-gray-50 rounded-lg",children:[l.jsx("img",{src:d.avatar,alt:d.name,className:"w-10 h-10 rounded-full object-cover"}),l.jsxs("div",{className:"flex-1",children:[l.jsx("div",{className:"font-medium text-gray-900",children:d.name}),l.jsxs("div",{className:"text-sm text-gray-500",children:[c," 条消息"]})]}),f&&l.jsx("div",{className:"text-xs text-gray-400",children:new Date(f.timestamp).toLocaleTimeString()})]},d.id)})})]}),l.jsxs("div",{className:"bg-white rounded-xl shadow-lg p-6",children:[l.jsxs("div",{className:"flex items-center gap-3 mb-4",children:[l.jsx(Ap,{size:24,className:"text-purple-600"}),l.jsx("h3",{className:"font-semibold text-gray-900",children:"讨论统计"})]}),l.jsxs("div",{className:"space-y-3",children:[l.jsxs("div",{className:"flex justify-between items-center",children:[l.jsx("span",{className:"text-gray-600",children:"总消息数"}),l.jsx("span",{className:"font-bold text-lg",children:o.totalMessages})]}),l.jsxs("div",{className:"flex justify-between items-center",children:[l.jsx("span",{className:"text-gray-600",children:"讨论时长"}),l.jsx("span",{className:"font-bold text-lg",children:v(o.activeTime)})]}),l.jsxs("div",{className:"flex justify-between items-center",children:[l.jsx("span",{className:"text-gray-600",children:"参与者数量"}),l.jsx("span",{className:"font-bold text-lg",children:N.length})]})]})]}),m.consensus&&l.jsxs("div",{className:"bg-green-50 border border-green-200 rounded-xl p-6",children:[l.jsxs("div",{className:"flex items-center gap-3 mb-3",children:[l.jsx(gn,{size:24,className:"text-green-600"}),l.jsx("h3",{className:"font-semibold text-green-900",children:"讨论结论"})]}),l.jsx("p",{className:"text-green-800",children:m.consensus})]})]})]})})})}function pm({message:e,agent:t}){const n=()=>{switch(e.type){case"question":return l.jsx(Up,{size:16,className:"text-blue-500"});case"agreement":return l.jsx(Yp,{size:16,className:"text-green-500"});case"disagreement":return l.jsx(Gp,{size:16,className:"text-red-500"});default:return l.jsx(Xe,{size:16,className:"text-gray-500"})}},r=()=>{switch(e.type){case"question":return"border-l-blue-500";case"agreement":return"border-l-green-500";case"disagreement":return"border-l-red-500";default:return"border-l-gray-300"}};return l.jsxs("div",{className:`flex gap-4 p-4 bg-gray-50 rounded-lg border-l-4 ${r()}`,children:[l.jsx("img",{src:t==null?void 0:t.avatar,alt:t==null?void 0:t.name,className:"w-12 h-12 rounded-full object-cover flex-shrink-0"}),l.jsxs("div",{className:"flex-1",children:[l.jsxs("div",{className:"flex items-center gap-2 mb-2",children:[l.jsx("span",{className:"font-semibold text-gray-900",children:t==null?void 0:t.name}),n(),l.jsx("span",{className:"text-xs text-gray-500",children:new Date(e.timestamp).toLocaleTimeString()})]}),l.jsx("p",{className:"text-gray-800 leading-relaxed",children:e.content})]})]})}const mm=({isOpen:e,onClose:t,onSave:n,editingConfig:r})=>{const[s,i]=M.useState({name:"",provider:"openai",model:"",apiKey:"",baseURL:"",temperature:.7,maxTokens:1e3,systemPrompt:""}),[o,a]=M.useState(!1),[u,p]=M.useState(!1),[h,m]=M.useState(null),[x,y]=M.useState([]),[w,v]=M.useState("");M.useEffect(()=>{r?(i(r),v("")):(i({name:"",provider:"openai",model:"",apiKey:"",baseURL:"",temperature:.7,maxTokens:1e3,systemPrompt:""}),v("")),m(null),y([])},[r,e]);const N=g=>{if(v(g),g){const j=ja.find(k=>k.id===g);j&&i(k=>({...k,name:j.name,provider:j.provider.toLowerCase(),model:j.model,temperature:j.defaultSettings.temperature,maxTokens:j.defaultSettings.maxTokens}))}},d=(g,j)=>{if(i(k=>({...k,[g]:j})),m(null),x.length>0){const k=I.validateLLMConfig({...s,[g]:j});y(k)}},c=async()=>{const g=I.validateLLMConfig(s);if(g.length>0){y(g);return}p(!0),m(null);try{const j={id:"test",name:s.name,provider:s.provider,model:s.model,apiKey:s.apiKey,baseURL:s.baseURL,temperature:s.temperature,maxTokens:s.maxTokens,systemPrompt:s.systemPrompt},k=await Hc.testLLMConfig(j);m({success:k,message:k?"连接测试成功！":"连接测试失败，请检查配置。"})}catch(j){m({success:!1,message:`连接测试失败: ${j instanceof Error?j.message:"未知错误"}`})}finally{p(!1)}},f=()=>{const g=I.validateLLMConfig(s);if(g.length>0){y(g);return}const j={id:(r==null?void 0:r.id)||I.generateLLMConfigId(),name:s.name,provider:s.provider,model:s.model,apiKey:s.apiKey,baseURL:s.baseURL,temperature:s.temperature,maxTokens:s.maxTokens,systemPrompt:s.systemPrompt};n(j),t()};return e?l.jsx("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50",children:l.jsxs("div",{className:"bg-white rounded-lg p-6 w-full max-w-2xl max-h-[90vh] overflow-y-auto",children:[l.jsxs("div",{className:"flex justify-between items-center mb-6",children:[l.jsx("h2",{className:"text-xl font-bold",children:r?"编辑LLM配置":"新建LLM配置"}),l.jsx("button",{onClick:t,className:"text-gray-500 hover:text-gray-700",children:l.jsx(Jp,{className:"w-6 h-6"})})]}),!r&&l.jsxs("div",{className:"mb-6",children:[l.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"选择预设配置（可选）"}),l.jsxs("select",{value:w,onChange:g=>N(g.target.value),className:"w-full p-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent",children:[l.jsx("option",{value:"",children:"自定义配置"}),ja.map(g=>l.jsxs("option",{value:g.id,children:[g.name," - ",g.description]},g.id))]})]}),x.length>0&&l.jsxs("div",{className:"mb-4 p-3 bg-red-50 border border-red-200 rounded-md",children:[l.jsxs("div",{className:"flex items-center",children:[l.jsx(hn,{className:"w-5 h-5 text-red-500 mr-2"}),l.jsx("span",{className:"text-red-700 font-medium",children:"配置错误"})]}),l.jsx("ul",{className:"mt-2 text-sm text-red-600",children:x.map((g,j)=>l.jsxs("li",{children:["• ",g]},j))})]}),h&&l.jsx("div",{className:`mb-4 p-3 border rounded-md ${h.success?"bg-green-50 border-green-200":"bg-red-50 border-red-200"}`,children:l.jsxs("div",{className:"flex items-center",children:[h.success?l.jsx(gn,{className:"w-5 h-5 text-green-500 mr-2"}):l.jsx(hn,{className:"w-5 h-5 text-red-500 mr-2"}),l.jsx("span",{className:`font-medium ${h.success?"text-green-700":"text-red-700"}`,children:h.message})]})}),l.jsxs("div",{className:"space-y-4",children:[l.jsxs("div",{children:[l.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"配置名称 *"}),l.jsx("input",{type:"text",value:s.name||"",onChange:g=>d("name",g.target.value),className:"w-full p-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent",placeholder:"例如：我的GPT-4配置"})]}),l.jsxs("div",{children:[l.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"提供商 *"}),l.jsxs("select",{value:s.provider||"openai",onChange:g=>d("provider",g.target.value),className:"w-full p-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent",children:[l.jsx("option",{value:"openai",children:"OpenAI"}),l.jsx("option",{value:"anthropic",children:"Anthropic"}),l.jsx("option",{value:"azure",children:"Azure OpenAI"}),l.jsx("option",{value:"custom",children:"自定义"})]})]}),l.jsxs("div",{children:[l.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"模型名称 *"}),l.jsx("input",{type:"text",value:s.model||"",onChange:g=>d("model",g.target.value),className:"w-full p-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent",placeholder:"例如：gpt-4, claude-3-opus-20240229"})]}),l.jsxs("div",{children:[l.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"API密钥 *"}),l.jsxs("div",{className:"relative",children:[l.jsx("input",{type:o?"text":"password",value:s.apiKey||"",onChange:g=>d("apiKey",g.target.value),className:"w-full p-2 pr-10 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent",placeholder:"输入API密钥"}),l.jsx("button",{type:"button",onClick:()=>a(!o),className:"absolute right-2 top-2 text-gray-500 hover:text-gray-700",children:o?l.jsx(Ip,{className:"w-5 h-5"}):l.jsx(Rp,{className:"w-5 h-5"})})]})]}),(s.provider==="azure"||s.provider==="custom")&&l.jsxs("div",{children:[l.jsxs("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:["基础URL ",s.provider==="azure"?"*":"(可选)"]}),l.jsx("input",{type:"text",value:s.baseURL||"",onChange:g=>d("baseURL",g.target.value),className:"w-full p-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent",placeholder:s.provider==="azure"?"https://your-resource.openai.azure.com":"https://api.example.com"})]}),l.jsxs("div",{className:"grid grid-cols-2 gap-4",children:[l.jsxs("div",{children:[l.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"温度 (0-2)"}),l.jsx("input",{type:"number",min:"0",max:"2",step:"0.1",value:s.temperature||.7,onChange:g=>d("temperature",parseFloat(g.target.value)),className:"w-full p-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"})]}),l.jsxs("div",{children:[l.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"最大令牌数"}),l.jsx("input",{type:"number",min:"1",max:"4000",value:s.maxTokens||1e3,onChange:g=>d("maxTokens",parseInt(g.target.value)),className:"w-full p-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"})]})]}),l.jsxs("div",{children:[l.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"自定义系统提示词 (可选)"}),l.jsx("textarea",{value:s.systemPrompt||"",onChange:g=>d("systemPrompt",g.target.value),rows:3,className:"w-full p-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent",placeholder:"添加额外的系统提示词..."})]})]}),l.jsxs("div",{className:"flex justify-between mt-6",children:[l.jsxs("button",{onClick:c,disabled:u,className:"flex items-center px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed",children:[l.jsx(Kp,{className:"w-4 h-4 mr-2"}),u?"测试中...":"测试连接"]}),l.jsxs("div",{className:"flex space-x-3",children:[l.jsx("button",{onClick:t,className:"px-4 py-2 text-gray-600 border border-gray-300 rounded-md hover:bg-gray-50",children:"取消"}),l.jsxs("button",{onClick:f,className:"flex items-center px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700",children:[l.jsx(Bp,{className:"w-4 h-4 mr-2"}),"保存"]})]})]})]})}):null},hm=()=>{const[e,t]=M.useState([]),[n,r]=M.useState(!1),[s,i]=M.useState(null),[o,a]=M.useState(!1),[u,p]=M.useState(!0),[h,m]=M.useState({total:0,byProvider:{},recentlyUsed:[]});M.useEffect(()=>{x()},[]);const x=async()=>{try{p(!0);const[f,g]=await Promise.all([I.getLLMConfigs(),I.getLLMConfigStats()]);t(f),m(g)}catch(f){console.error("Failed to load configs:",f),t([]),m({total:0,byProvider:{},recentlyUsed:[]})}finally{p(!1)}},y=async f=>{try{await I.saveLLMConfig(f),await x()}catch{alert("保存配置失败")}},w=f=>{i(f),r(!0)},v=async f=>{if(confirm("确定要删除这个LLM配置吗？"))try{await I.deleteLLMConfig(f),await x()}catch{alert("删除配置失败")}},N=()=>{i(null),r(!0)},d=async()=>{try{const f=await I.exportLLMConfigs(),g=new Blob([f],{type:"application/json"}),j=URL.createObjectURL(g),k=document.createElement("a");k.href=j,k.download=`llm-configs-${new Date().toISOString().split("T")[0]}.json`,document.body.appendChild(k),k.click(),document.body.removeChild(k),URL.revokeObjectURL(j)}catch{alert("导出失败")}},c=f=>{var k;const g=(k=f.target.files)==null?void 0:k[0];if(!g)return;const j=new FileReader;j.onload=async L=>{var b;try{const $=(b=L.target)==null?void 0:b.result,A=await I.importLLMConfigs($);A.success>0&&(alert(`成功导入 ${A.success} 个配置`),await x()),A.errors.length>0&&alert(`导入时遇到错误：
${A.errors.join(`
`)}`)}catch{alert("导入失败：文件格式错误")}},j.readAsText(g),f.target.value=""};return u?l.jsx("div",{className:"h-full overflow-y-auto",children:l.jsx("div",{className:"centered-container",children:l.jsx("div",{className:"centered-content",children:l.jsx("div",{className:"flex items-center justify-center h-64",children:l.jsxs("div",{className:"text-center",children:[l.jsx("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"}),l.jsx("p",{className:"text-gray-600",children:"正在加载LLM配置..."})]})})})})}):l.jsx("div",{className:"h-full overflow-y-auto",children:l.jsx("div",{className:"centered-container",children:l.jsxs("div",{className:"centered-content",children:[l.jsxs("div",{className:"flex justify-between items-center mb-6",children:[l.jsxs("div",{children:[l.jsxs("h2",{className:"text-2xl font-bold text-gray-900 flex items-center",children:[l.jsx(xt,{className:"w-8 h-8 mr-3 text-blue-600"}),"LLM配置管理"]}),l.jsx("p",{className:"text-gray-600 mt-1",children:"管理大语言模型配置，为智能体提供AI能力"})]}),l.jsxs("div",{className:"flex space-x-3",children:[l.jsxs("button",{onClick:()=>a(!o),className:"flex items-center px-4 py-2 text-gray-600 border border-gray-300 rounded-md hover:bg-gray-50",children:[l.jsx(jn,{className:"w-4 h-4 mr-2"}),"导入/导出"]}),l.jsxs("button",{onClick:N,className:"flex items-center px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700",children:[l.jsx(ai,{className:"w-4 h-4 mr-2"}),"新建配置"]})]})]}),l.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4 mb-6",children:[l.jsxs("div",{className:"bg-white p-4 rounded-lg border border-gray-200",children:[l.jsx("div",{className:"text-2xl font-bold text-blue-600",children:h.total}),l.jsx("div",{className:"text-sm text-gray-600",children:"总配置数"})]}),l.jsxs("div",{className:"bg-white p-4 rounded-lg border border-gray-200",children:[l.jsx("div",{className:"text-2xl font-bold text-green-600",children:Object.keys(h.byProvider).length}),l.jsx("div",{className:"text-sm text-gray-600",children:"支持的提供商"})]}),l.jsxs("div",{className:"bg-white p-4 rounded-lg border border-gray-200",children:[l.jsx("div",{className:"text-2xl font-bold text-purple-600",children:h.recentlyUsed.length}),l.jsx("div",{className:"text-sm text-gray-600",children:"最近使用"})]})]}),o&&l.jsxs("div",{className:"bg-gray-50 p-4 rounded-lg mb-6 border border-gray-200",children:[l.jsx("h3",{className:"text-lg font-medium mb-3",children:"导入/导出配置"}),l.jsxs("div",{className:"flex space-x-4",children:[l.jsxs("button",{onClick:d,className:"flex items-center px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700",children:[l.jsx(oi,{className:"w-4 h-4 mr-2"}),"导出配置"]}),l.jsxs("label",{className:"flex items-center px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 cursor-pointer",children:[l.jsx(ui,{className:"w-4 h-4 mr-2"}),"导入配置",l.jsx("input",{type:"file",accept:".json",onChange:c,className:"hidden"})]})]}),l.jsx("p",{className:"text-sm text-gray-600 mt-2",children:"导出的配置文件会隐藏API密钥，导入时需要重新设置"})]}),l.jsx("div",{className:"bg-white rounded-lg border border-gray-200",children:e.length===0?l.jsxs("div",{className:"p-8 text-center",children:[l.jsx(xt,{className:"w-16 h-16 text-gray-300 mx-auto mb-4"}),l.jsx("h3",{className:"text-lg font-medium text-gray-900 mb-2",children:"还没有LLM配置"}),l.jsx("p",{className:"text-gray-600 mb-4",children:"创建第一个LLM配置来为智能体提供AI能力"}),l.jsxs("button",{onClick:N,className:"flex items-center px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 mx-auto",children:[l.jsx(ai,{className:"w-4 h-4 mr-2"}),"新建配置"]})]}):l.jsx("div",{className:"divide-y divide-gray-200",children:e.map(f=>l.jsxs("div",{className:"p-4 hover:bg-gray-50",children:[l.jsxs("div",{className:"flex items-center justify-between",children:[l.jsxs("div",{className:"flex items-center space-x-4",children:[l.jsx("div",{className:"text-2xl",children:xs(f.provider)}),l.jsxs("div",{children:[l.jsx("h3",{className:"text-lg font-medium text-gray-900",children:f.name}),l.jsxs("div",{className:"flex items-center space-x-2 mt-1",children:[l.jsx("span",{className:`px-2 py-1 text-xs rounded-full ${ys(f.provider)}`,children:f.provider.toUpperCase()}),l.jsx("span",{className:"text-sm text-gray-600",children:f.model})]})]})]}),l.jsxs("div",{className:"flex items-center space-x-2",children:[l.jsxs("div",{className:"text-right text-sm text-gray-600",children:[l.jsxs("div",{children:["温度: ",f.temperature]}),l.jsxs("div",{children:["令牌: ",f.maxTokens]})]}),l.jsxs("div",{className:"flex space-x-1",children:[l.jsx("button",{onClick:()=>w(f),className:"p-2 text-gray-600 hover:text-blue-600 hover:bg-blue-50 rounded-md",title:"编辑配置",children:l.jsx(Uc,{className:"w-4 h-4"})}),l.jsx("button",{onClick:()=>v(f.id),className:"p-2 text-gray-600 hover:text-red-600 hover:bg-red-50 rounded-md",title:"删除配置",children:l.jsx(ar,{className:"w-4 h-4"})})]})]})]}),f.systemPrompt&&l.jsxs("div",{className:"mt-3 p-3 bg-gray-50 rounded-md",children:[l.jsx("div",{className:"text-sm text-gray-600",children:l.jsx("strong",{children:"自定义系统提示词:"})}),l.jsx("div",{className:"text-sm text-gray-800 mt-1 line-clamp-2",children:f.systemPrompt})]})]},f.id))})}),Object.keys(h.byProvider).length>0&&l.jsxs("div",{className:"mt-6 bg-white p-4 rounded-lg border border-gray-200",children:[l.jsx("h3",{className:"text-lg font-medium mb-3",children:"提供商分布"}),l.jsx("div",{className:"flex flex-wrap gap-2",children:Object.entries(h.byProvider).map(([f,g])=>l.jsxs("div",{className:`flex items-center px-3 py-1 rounded-full text-sm ${ys(f)}`,children:[l.jsx("span",{className:"mr-1",children:xs(f)}),f.toUpperCase(),": ",g]},f))})]}),l.jsx(mm,{isOpen:n,onClose:()=>{r(!1),i(null)},onSave:y,editingConfig:s})]})})})},gm=()=>{const{state:e,exportData:t,importData:n,clearAllData:r}=St(),[s,i]=M.useState(!1),[o,a]=M.useState(!1),[u,p]=M.useState(null),[h,m]=M.useState(!1),[x,y]=M.useState(null);M.useEffect(()=>{(async()=>{try{const f=await I.getStorageInfo();y(f)}catch(f){console.error("Failed to fetch storage info:",f),y({used:0,available:0,total:0})}})()},[]);const w=c=>{if(c===0)return"0 Bytes";const f=1024,g=["Bytes","KB","MB","GB"],j=Math.floor(Math.log(c)/Math.log(f));return parseFloat((c/Math.pow(f,j)).toFixed(2))+" "+g[j]},v=async()=>{i(!0);try{const c=await t(),f=new Blob([c],{type:"application/json"}),g=URL.createObjectURL(f),j=document.createElement("a");j.href=g,j.download=`multi-agent-system-backup-${new Date().toISOString().split("T")[0]}.json`,document.body.appendChild(j),j.click(),document.body.removeChild(j),URL.revokeObjectURL(g)}catch(c){p({success:!1,message:"导出失败: "+(c instanceof Error?c.message:"未知错误")})}finally{i(!1)}},N=async c=>{var g;const f=(g=c.target.files)==null?void 0:g[0];if(f){a(!0),p(null);try{const j=await f.text(),k=await n(j);p({success:k,message:k?"数据导入成功！":"数据导入失败，请检查文件格式。"})}catch(j){p({success:!1,message:"导入失败: "+(j instanceof Error?j.message:"未知错误")})}finally{a(!1),c.target.value=""}}},d=async()=>{try{await r(),m(!1),p({success:!0,message:"所有数据已清除，系统已重置为默认状态。"})}catch(c){p({success:!1,message:"清除数据失败: "+(c instanceof Error?c.message:"未知错误")})}};return l.jsx("div",{className:"h-full overflow-y-auto",children:l.jsx("div",{className:"centered-container",children:l.jsxs("div",{className:"centered-content",children:[l.jsx("div",{className:"flex justify-between items-center mb-6",children:l.jsxs("div",{children:[l.jsxs("h2",{className:"text-2xl font-bold text-gray-900 flex items-center",children:[l.jsx(lo,{className:"w-8 h-8 mr-3 text-blue-600"}),"数据管理"]}),l.jsx("p",{className:"text-gray-600 mt-1",children:"管理系统数据的备份、恢复和清理"})]})}),u&&l.jsx("div",{className:`mb-6 p-4 rounded-lg border ${u.success?"bg-green-50 border-green-200":"bg-red-50 border-red-200"}`,children:l.jsxs("div",{className:"flex items-center",children:[u.success?l.jsx(gn,{className:"w-5 h-5 text-green-500 mr-2"}):l.jsx(ii,{className:"w-5 h-5 text-red-500 mr-2"}),l.jsx("span",{className:`font-medium ${u.success?"text-green-700":"text-red-700"}`,children:u.message})]})}),l.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4 mb-8",children:[l.jsx("div",{className:"bg-white p-4 rounded-lg border border-gray-200",children:l.jsxs("div",{className:"flex items-center justify-between",children:[l.jsxs("div",{children:[l.jsx("div",{className:"text-2xl font-bold text-blue-600",children:e.agents.length}),l.jsx("div",{className:"text-sm text-gray-600",children:"智能体"})]}),l.jsx(jn,{className:"w-8 h-8 text-blue-500"})]})}),l.jsx("div",{className:"bg-white p-4 rounded-lg border border-gray-200",children:l.jsxs("div",{className:"flex items-center justify-between",children:[l.jsxs("div",{children:[l.jsx("div",{className:"text-2xl font-bold text-green-600",children:e.allDiscussions.length}),l.jsx("div",{className:"text-sm text-gray-600",children:"历史讨论"})]}),l.jsx(Op,{className:"w-8 h-8 text-green-500"})]})}),l.jsx("div",{className:"bg-white p-4 rounded-lg border border-gray-200",children:l.jsxs("div",{className:"flex items-center justify-between",children:[l.jsxs("div",{children:[l.jsx("div",{className:"text-2xl font-bold text-purple-600",children:x?w(x.used):"加载中..."}),l.jsx("div",{className:"text-sm text-gray-600",children:"已用存储"})]}),l.jsx($p,{className:"w-8 h-8 text-purple-500"})]})})]}),l.jsxs("div",{className:"bg-white p-6 rounded-lg border border-gray-200 mb-8",children:[l.jsx("h3",{className:"text-lg font-medium mb-4",children:"存储使用情况"}),x?l.jsxs("div",{className:"space-y-3",children:[l.jsxs("div",{className:"flex justify-between text-sm",children:[l.jsx("span",{children:"已使用"}),l.jsx("span",{children:w(x.used)})]}),l.jsx("div",{className:"w-full bg-gray-200 rounded-full h-2",children:l.jsx("div",{className:"bg-blue-500 h-2 rounded-full transition-all",style:{width:`${x.used/x.total*100}%`}})}),l.jsxs("div",{className:"flex justify-between text-sm text-gray-600",children:[l.jsxs("span",{children:["可用: ",w(x.available)]}),l.jsxs("span",{children:["总计: ",w(x.total)]})]})]}):l.jsx("div",{className:"text-center text-gray-500",children:"加载存储信息中..."})]}),l.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-6",children:[l.jsxs("div",{className:"bg-white p-6 rounded-lg border border-gray-200",children:[l.jsxs("div",{className:"flex items-center mb-4",children:[l.jsx(oi,{className:"w-6 h-6 text-green-600 mr-3"}),l.jsx("h3",{className:"text-lg font-medium",children:"导出数据"})]}),l.jsx("p",{className:"text-gray-600 mb-4 text-sm",children:"将所有配置和历史记录导出为JSON文件，用于备份或迁移。"}),l.jsxs("button",{onClick:v,disabled:s,className:"w-full flex items-center justify-center px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 disabled:opacity-50 disabled:cursor-not-allowed",children:[l.jsx(oi,{className:"w-4 h-4 mr-2"}),s?"导出中...":"导出数据"]})]}),l.jsxs("div",{className:"bg-white p-6 rounded-lg border border-gray-200",children:[l.jsxs("div",{className:"flex items-center mb-4",children:[l.jsx(ui,{className:"w-6 h-6 text-blue-600 mr-3"}),l.jsx("h3",{className:"text-lg font-medium",children:"导入数据"})]}),l.jsx("p",{className:"text-gray-600 mb-4 text-sm",children:"从备份文件恢复配置和历史记录。将覆盖当前数据。"}),l.jsxs("label",{className:"w-full flex items-center justify-center px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 cursor-pointer",children:[l.jsx(ui,{className:"w-4 h-4 mr-2"}),o?"导入中...":"选择文件",l.jsx("input",{type:"file",accept:".json",onChange:N,disabled:o,className:"hidden"})]})]}),l.jsxs("div",{className:"bg-white p-6 rounded-lg border border-gray-200",children:[l.jsxs("div",{className:"flex items-center mb-4",children:[l.jsx(ar,{className:"w-6 h-6 text-red-600 mr-3"}),l.jsx("h3",{className:"text-lg font-medium",children:"清除数据"})]}),l.jsx("p",{className:"text-gray-600 mb-4 text-sm",children:"清除所有数据并重置为默认状态。此操作不可撤销。"}),h?l.jsxs("div",{className:"space-y-2",children:[l.jsxs("div",{className:"flex items-center p-2 bg-red-50 border border-red-200 rounded text-sm text-red-700",children:[l.jsx(ii,{className:"w-4 h-4 mr-2"}),"确定要清除所有数据吗？"]}),l.jsxs("div",{className:"flex space-x-2",children:[l.jsx("button",{onClick:d,className:"flex-1 px-3 py-2 bg-red-600 text-white rounded text-sm hover:bg-red-700",children:"确认清除"}),l.jsx("button",{onClick:()=>m(!1),className:"flex-1 px-3 py-2 bg-gray-300 text-gray-700 rounded text-sm hover:bg-gray-400",children:"取消"})]})]}):l.jsxs("button",{onClick:()=>m(!0),className:"w-full flex items-center justify-center px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700",children:[l.jsx(ar,{className:"w-4 h-4 mr-2"}),"清除所有数据"]})]})]}),l.jsx("div",{className:"mt-8 bg-blue-50 border border-blue-200 rounded-lg p-4",children:l.jsxs("div",{className:"flex items-start",children:[l.jsx(Fp,{className:"w-5 h-5 text-blue-500 mr-2 mt-0.5"}),l.jsxs("div",{className:"text-sm text-blue-700",children:[l.jsx("div",{className:"font-medium mb-1",children:"使用说明："}),l.jsxs("ul",{className:"space-y-1 text-xs",children:[l.jsx("li",{children:"• 导出的数据包含智能体配置、LLM配置、讨论历史等所有信息"}),l.jsx("li",{children:"• 导入数据会覆盖当前所有配置，建议先导出备份"}),l.jsx("li",{children:"• 清除数据会删除所有自定义配置，但会保留默认智能体"}),l.jsx("li",{children:"• 数据存储在浏览器本地，清除浏览器数据会丢失所有配置"})]})]})]})})]})})})},xm=()=>{const{state:e,dispatch:t}=St(),[n,r]=M.useState(""),[s,i]=M.useState("date"),[o,a]=M.useState("all"),[u,p]=M.useState(null),[h,m]=M.useState(null),x=async N=>{if(confirm("确定要删除这条讨论记录吗？此操作不可撤销。"))try{m(N),await I.deleteDiscussion(N);const d=await I.getDiscussions();t({type:"SET_ALL_DISCUSSIONS",payload:d})}catch(d){console.error("删除讨论失败:",d),alert("删除失败，请重试")}finally{m(null)}},y=e.allDiscussions.filter(N=>{const d=N.topic.toLowerCase().includes(n.toLowerCase()),c=o==="all"||N.status===o;return d&&c}).sort((N,d)=>{switch(s){case"date":return new Date(d.createdAt).getTime()-new Date(N.createdAt).getTime();case"topic":return N.topic.localeCompare(d.topic);case"messages":return d.messages.length-N.messages.length;default:return 0}}),w=N=>{switch(N){case"consensus":return l.jsx(gn,{className:"w-4 h-4 text-green-500"});case"ended":return l.jsx(Oc,{className:"w-4 h-4 text-gray-500"});default:return l.jsx(Xe,{className:"w-4 h-4 text-blue-500"})}},v=N=>{switch(N){case"consensus":return"已达成共识";case"ended":return"已结束";default:return"进行中"}};return l.jsx("div",{className:"h-[calc(100vh-4rem)] bg-gradient-to-br from-gray-50 to-blue-50 w-full overflow-hidden",children:l.jsx("div",{className:"h-full p-6",children:l.jsxs("div",{className:"bg-white rounded-xl shadow-lg h-full flex flex-col",children:[l.jsxs("div",{className:"bg-gradient-to-r from-purple-600 to-blue-600 text-white p-6 rounded-t-xl",children:[l.jsxs("div",{className:"flex items-center gap-3 mb-4",children:[l.jsx(gs,{size:32}),l.jsxs("div",{children:[l.jsx("h1",{className:"text-2xl font-bold",children:"讨论历史"}),l.jsx("p",{className:"text-purple-100",children:"查看和管理历史讨论记录"})]})]}),l.jsxs("div",{className:"flex gap-4 items-center",children:[l.jsxs("div",{className:"flex-1 relative",children:[l.jsx(Wp,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4"}),l.jsx("input",{type:"text",placeholder:"搜索讨论话题...",value:n,onChange:N=>r(N.target.value),className:"w-full pl-10 pr-4 py-2 rounded-lg text-gray-900 placeholder-gray-500"})]}),l.jsxs("select",{value:s,onChange:N=>i(N.target.value),className:"px-4 py-2 rounded-lg text-gray-900",children:[l.jsx("option",{value:"date",children:"按时间排序"}),l.jsx("option",{value:"topic",children:"按话题排序"}),l.jsx("option",{value:"messages",children:"按消息数排序"})]}),l.jsxs("select",{value:o,onChange:N=>a(N.target.value),className:"px-4 py-2 rounded-lg text-gray-900",children:[l.jsx("option",{value:"all",children:"全部状态"}),l.jsx("option",{value:"consensus",children:"已达成共识"}),l.jsx("option",{value:"ended",children:"已结束"})]})]})]}),l.jsx("div",{className:"flex-1 overflow-y-auto p-6",children:y.length===0?l.jsxs("div",{className:"text-center py-12",children:[l.jsx(gs,{size:64,className:"text-gray-400 mx-auto mb-4"}),l.jsx("h3",{className:"text-xl font-semibold text-gray-900 mb-2",children:"暂无讨论记录"}),l.jsx("p",{className:"text-gray-600",children:"开始一个新的讨论来创建历史记录"})]}):l.jsx("div",{className:"space-y-4",children:y.map(N=>{const d=e.agents.filter(c=>N.participants.includes(c.id));return l.jsx("div",{className:"bg-gray-50 rounded-lg border border-gray-200",children:l.jsxs("div",{className:"p-4",children:[l.jsxs("div",{className:"flex items-center justify-between",children:[l.jsxs("div",{className:"flex-1",children:[l.jsxs("div",{className:"flex items-center gap-3 mb-2",children:[w(N.status),l.jsx("h3",{className:"font-semibold text-gray-900",children:N.topic}),l.jsx("span",{className:"text-sm text-gray-500",children:v(N.status)})]}),l.jsxs("div",{className:"flex items-center gap-6 text-sm text-gray-600",children:[l.jsxs("div",{className:"flex items-center gap-1",children:[l.jsx(Pp,{className:"w-4 h-4"}),new Date(N.createdAt).toLocaleString()]}),l.jsxs("div",{className:"flex items-center gap-1",children:[l.jsx(Xe,{className:"w-4 h-4"}),N.messages.length," 条消息"]}),l.jsxs("div",{className:"flex items-center gap-1",children:[l.jsx(xn,{className:"w-4 h-4"}),d.length," 位参与者"]}),N.consensusScore!==void 0&&l.jsxs("div",{className:"flex items-center gap-1",children:[l.jsx(Fc,{className:"w-4 h-4"}),"共识度 ",Math.round(N.consensusScore),"%"]})]})]}),l.jsxs("div",{className:"flex items-center gap-2",children:[l.jsx("button",{onClick:()=>x(N.id),disabled:h===N.id,className:"p-2 text-gray-600 hover:text-red-600 hover:bg-red-50 rounded-md disabled:opacity-50 disabled:cursor-not-allowed transition-colors",title:"删除记录",children:h===N.id?l.jsx("div",{className:"animate-spin w-4 h-4 border-2 border-red-500 border-t-transparent rounded-full"}):l.jsx(ar,{className:"w-4 h-4"})}),l.jsx("button",{onClick:()=>p(u===N.id?null:N.id),className:"p-2 text-gray-600 hover:text-purple-600 hover:bg-purple-50 rounded-md transition-colors",title:"查看详情",children:u===N.id?l.jsx(zp,{className:"w-4 h-4"}):l.jsx(_p,{className:"w-4 h-4"})})]})]}),u===N.id&&l.jsxs("div",{className:"mt-4 pt-4 border-t border-gray-200",children:[l.jsxs("div",{className:"mb-4",children:[l.jsx("h4",{className:"font-medium text-gray-900 mb-2",children:"参与者"}),l.jsx("div",{className:"flex flex-wrap gap-2",children:d.map(c=>l.jsxs("div",{className:"flex items-center gap-2 bg-white px-3 py-1 rounded-full border",children:[l.jsx("img",{src:c.avatar,alt:c.name,className:"w-6 h-6 rounded-full object-cover"}),l.jsx("span",{className:"text-sm font-medium",children:c.name})]},c.id))})]}),N.consensus&&l.jsxs("div",{className:"mb-4",children:[l.jsx("h4",{className:"font-medium text-gray-900 mb-2",children:"讨论结论"}),l.jsx("div",{className:"bg-green-50 border border-green-200 rounded-lg p-3",children:l.jsx("p",{className:"text-green-800",children:N.consensus})})]}),l.jsxs("div",{children:[l.jsx("h4",{className:"font-medium text-gray-900 mb-2",children:"消息预览"}),l.jsxs("div",{className:"space-y-2 max-h-60 overflow-y-auto",children:[N.messages.slice(0,5).map(c=>{const f=d.find(g=>g.id===c.agentId);return l.jsxs("div",{className:"bg-white p-3 rounded border",children:[l.jsxs("div",{className:"flex items-center gap-2 mb-1",children:[l.jsx("span",{className:"font-medium text-sm",children:f==null?void 0:f.name}),l.jsx("span",{className:"text-xs text-gray-500",children:new Date(c.timestamp).toLocaleTimeString()})]}),l.jsx("p",{className:"text-sm text-gray-700 line-clamp-2",children:c.content})]},c.id)}),N.messages.length>5&&l.jsxs("div",{className:"text-center text-sm text-gray-500",children:["还有 ",N.messages.length-5," 条消息..."]})]})]})]})]})},N.id)})})})]})})})},ym=()=>{const{state:e}=St(),[t,n]=M.useState(!1),[r,s]=M.useState([{id:"storage",label:"初始化存储服务",status:"pending"},{id:"server",label:"检查服务器连接",status:"pending"},{id:"agents",label:"加载智能体配置",status:"pending"},{id:"llm",label:"加载LLM配置",status:"pending"},{id:"discussions",label:"加载讨论历史",status:"pending"}]);M.useEffect(()=>{s(u=>u.map(p=>{const h=u.findIndex(x=>x.id===p.id),m=u.findIndex(x=>x.id===e.loadingStep);return h<m?{...p,status:"completed"}:h===m?{...p,status:"loading"}:{...p,status:"pending"}}))},[e.loadingStep]),M.useEffect(()=>{const u=setTimeout(()=>{n(!0)},1e4);return()=>clearTimeout(u)},[]);const i=u=>{switch(u){case"completed":return l.jsx(gn,{className:"w-4 h-4 text-green-500"});case"loading":return l.jsx(ya,{className:"w-4 h-4 text-blue-500 animate-spin"});case"error":return l.jsx(hn,{className:"w-4 h-4 text-red-500"});default:return l.jsx(Oc,{className:"w-4 h-4 text-gray-400"})}},o=r.filter(u=>u.status==="completed").length,a=o/r.length*100;return l.jsx("div",{className:"min-h-screen bg-gradient-to-br from-indigo-50 via-blue-50 to-purple-50 flex items-center justify-center",children:l.jsxs("div",{className:"text-center max-w-md mx-auto",children:[l.jsxs("div",{className:"flex items-center justify-center gap-3 mb-8",children:[l.jsx(xt,{size:48,className:"text-blue-600 animate-pulse"}),l.jsx("h1",{className:"text-4xl font-bold text-gray-900",children:"多智能体讨论系统"})]}),l.jsxs("div",{className:"flex items-center justify-center gap-3 mb-6",children:[l.jsx(ya,{className:"w-6 h-6 text-blue-600 animate-spin"}),l.jsx("span",{className:"text-lg text-gray-600",children:"正在初始化系统..."})]}),l.jsxs("div",{className:"w-80 mx-auto mb-6",children:[l.jsx("div",{className:"w-full bg-gray-200 rounded-full h-2",children:l.jsx("div",{className:"bg-blue-500 h-2 rounded-full transition-all duration-500",style:{width:`${a}%`}})}),l.jsxs("div",{className:"mt-2 text-sm text-gray-500",children:[o,"/",r.length," 步骤完成"]})]}),l.jsx("div",{className:"space-y-3 text-sm",children:r.map(u=>l.jsxs("div",{className:"flex items-center justify-center gap-3",children:[i(u.status),l.jsx("span",{className:`${u.status==="completed"?"text-green-600":u.status==="loading"?"text-blue-600":u.status==="error"?"text-red-600":"text-gray-500"}`,children:u.label})]},u.id))}),t&&l.jsxs("div",{className:"mt-6 p-4 bg-yellow-50 border border-yellow-200 rounded-lg",children:[l.jsxs("div",{className:"flex items-center gap-2 text-yellow-800",children:[l.jsx(hn,{className:"w-5 h-5"}),l.jsx("span",{className:"font-medium",children:"加载时间较长"})]}),l.jsx("p",{className:"text-sm text-yellow-700 mt-1",children:"连接服务器超时，原因是网络较慢或后台服务不可用。"})]})]})})};class vm extends M.Component{constructor(n){super(n);Ce(this,"handleReload",()=>{window.location.reload()});Ce(this,"handleReset",()=>{this.setState({hasError:!1,error:null,errorInfo:null})});this.state={hasError:!1,error:null,errorInfo:null}}static getDerivedStateFromError(n){return{hasError:!0,error:n,errorInfo:null}}componentDidCatch(n,r){console.error("ErrorBoundary caught an error:",n,r),this.setState({error:n,errorInfo:r})}render(){var n,r,s;if(this.state.hasError){const i=(r=(n=this.state.error)==null?void 0:n.message)==null?void 0:r.includes("无法连接到后端服务器");return l.jsx("div",{className:"min-h-screen bg-gradient-to-br from-red-50 via-orange-50 to-yellow-50 flex items-center justify-center",children:l.jsxs("div",{className:"text-center max-w-2xl mx-auto p-8",children:[l.jsxs("div",{className:"flex items-center justify-center gap-3 mb-6",children:[l.jsx(ii,{size:48,className:"text-red-500"}),l.jsx("h1",{className:"text-3xl font-bold text-gray-900",children:i?"无法连接后端服务":"系统初始化失败"})]}),l.jsxs("div",{className:"bg-white rounded-lg shadow-lg p-6 mb-6",children:[l.jsx("h2",{className:"text-lg font-semibold text-gray-800 mb-4",children:"错误详情"}),l.jsx("div",{className:"text-left bg-gray-50 rounded p-4 mb-4",children:l.jsx("p",{className:"text-red-600 font-mono text-sm",children:((s=this.state.error)==null?void 0:s.message)||"未知错误"})}),l.jsx("div",{className:"text-sm text-gray-600 mb-4",children:i?l.jsxs(l.Fragment,{children:[l.jsx("p",{className:"font-medium mb-2",children:"请检查以下项目："}),l.jsxs("ul",{className:"list-disc list-inside space-y-1",children:[l.jsx("li",{children:"后端服务是否已启动（端口5000）"}),l.jsx("li",{children:"网络连接是否正常"}),l.jsx("li",{children:"防火墙是否阻止了连接"}),l.jsx("li",{children:"后端服务地址配置是否正确"})]})]}):l.jsxs(l.Fragment,{children:[l.jsx("p",{children:"可能的原因："}),l.jsxs("ul",{className:"list-disc list-inside mt-2 space-y-1",children:[l.jsx("li",{children:"网络连接问题"}),l.jsx("li",{children:"后端服务器不可用"}),l.jsx("li",{children:"浏览器存储空间不足"}),l.jsx("li",{children:"配置文件损坏"})]})]})})]}),l.jsxs("div",{className:"flex gap-4 justify-center",children:[l.jsxs("button",{onClick:this.handleReload,className:"flex items-center gap-2 px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors",children:[l.jsx(Hp,{size:20}),"重新加载页面"]}),l.jsx("button",{onClick:this.handleReset,className:"flex items-center gap-2 px-6 py-3 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors",children:"重试初始化"})]}),i&&l.jsxs("div",{className:"mt-6 p-4 bg-blue-50 border border-blue-200 rounded-lg",children:[l.jsx("p",{className:"text-blue-800 font-medium mb-2",children:"启动后端服务："}),l.jsxs("div",{className:"text-sm text-blue-700 space-y-1",children:[l.jsxs("p",{children:["Windows: 运行 ",l.jsx("code",{className:"bg-blue-100 px-1 rounded",children:"start.bat"})]}),l.jsxs("p",{children:["Linux/macOS: 运行 ",l.jsx("code",{className:"bg-blue-100 px-1 rounded",children:"./start.sh"})]})]})]})]})})}return this.props.children}}function wm(){const{state:e}=St(),[t,n]=M.useState("home");if(Pa.useEffect(()=>{e.isDiscussionActive&&e.currentDiscussion&&n("discussion")},[e.isDiscussionActive,e.currentDiscussion]),e.isLoading)return l.jsx(ym,{});const r=()=>{switch(t){case"agents":return l.jsx(rm,{});case"llm":return l.jsx(hm,{});case"data":return l.jsx(gm,{});case"history":return l.jsx(xm,{});case"setup":return l.jsx(im,{});case"discussion":return l.jsx(fm,{});default:return l.jsx(jm,{onNavigate:n})}};return l.jsxs("div",{className:"h-screen bg-gray-50 w-full flex flex-col",children:[t!=="home"&&l.jsx("nav",{className:"flex-shrink-0 bg-white shadow-sm border-b border-gray-200 w-full",children:l.jsx("div",{className:"w-full px-6",children:l.jsxs("div",{className:"flex items-center justify-between h-16",children:[l.jsxs("div",{className:"flex items-center gap-8",children:[l.jsxs("button",{onClick:()=>n("home"),className:"flex items-center gap-2 text-gray-900 hover:text-blue-600 font-medium",children:[l.jsx(xt,{size:24,className:"text-blue-600"}),"多智能体讨论系统"]}),l.jsxs("div",{className:"flex gap-6",children:[l.jsxs("button",{onClick:()=>n("agents"),className:`flex items-center gap-2 px-3 py-2 rounded-md transition-colors ${t==="agents"?"bg-blue-100 text-blue-700":"text-gray-600 hover:text-gray-900"}`,children:[l.jsx(xn,{size:20}),"智能体管理"]}),l.jsxs("button",{onClick:()=>n("llm"),className:`flex items-center gap-2 px-3 py-2 rounded-md transition-colors ${t==="llm"?"bg-orange-100 text-orange-700":"text-gray-600 hover:text-gray-900"}`,children:[l.jsx($c,{size:20}),"LLM管理"]}),l.jsxs("button",{onClick:()=>n("history"),className:`flex items-center gap-2 px-3 py-2 rounded-md transition-colors ${t==="history"?"bg-purple-100 text-purple-700":"text-gray-600 hover:text-gray-900"}`,children:[l.jsx(gs,{size:20}),"讨论历史"]}),l.jsxs("button",{onClick:()=>n("data"),className:`flex items-center gap-2 px-3 py-2 rounded-md transition-colors ${t==="data"?"bg-indigo-100 text-indigo-700":"text-gray-600 hover:text-gray-900"}`,children:[l.jsx(lo,{size:20}),"数据管理"]}),l.jsxs("button",{onClick:()=>n("setup"),className:`flex items-center gap-2 px-3 py-2 rounded-md transition-colors ${t==="setup"?"bg-blue-100 text-blue-700":"text-gray-600 hover:text-gray-900"}`,disabled:e.agents.length===0,children:[l.jsx(jn,{size:20}),"创建讨论"]}),e.isDiscussionActive&&l.jsxs("button",{onClick:()=>n("discussion"),className:`flex items-center gap-2 px-3 py-2 rounded-md transition-colors ${t==="discussion"?"bg-green-100 text-green-700":"text-green-600 hover:text-green-700"}`,children:[l.jsx(Xe,{size:20}),"讨论进行中",l.jsx("div",{className:"w-2 h-2 bg-green-500 rounded-full animate-pulse"})]})]})]}),l.jsxs("div",{className:"flex items-center gap-4",children:[l.jsxs("span",{className:"text-sm text-gray-500",children:[e.agents.length," 个智能体"]}),e.isDiscussionActive&&l.jsxs("div",{className:"flex items-center gap-2 bg-green-50 text-green-700 px-3 py-1 rounded-full text-sm",children:[l.jsx("div",{className:"w-2 h-2 bg-green-500 rounded-full"}),"讨论中"]})]})]})})}),l.jsx("div",{className:"flex-1 overflow-hidden",children:r()})]})}function jm({onNavigate:e}){var n;const{state:t}=St();return l.jsx("div",{className:"h-full bg-gradient-to-br from-indigo-50 via-blue-50 to-purple-50 w-full fixed-layout overflow-y-auto",children:l.jsxs("div",{className:"w-full px-6 py-12 fixed-layout",children:[l.jsxs("div",{className:"text-center mb-16",children:[l.jsxs("div",{className:"flex items-center justify-center gap-3 mb-6",children:[l.jsx(xt,{size:48,className:"text-blue-600"}),l.jsx("h1",{className:"text-5xl font-bold text-gray-900",children:"多智能体讨论系统"})]}),l.jsx("p",{className:"text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed",children:"通过配置不同专业背景和思维方式的AI智能体，创建富有洞察力的讨论环境， 探索复杂问题的多维度解决方案，并达成有价值的共识。"}),l.jsxs("div",{className:"flex items-center justify-center gap-8 mt-8 text-sm text-gray-500",children:[l.jsxs("div",{className:"flex items-center gap-2",children:[l.jsx(wa,{size:16,className:"text-yellow-500"}),"支持2-8个智能体同时讨论"]}),l.jsxs("div",{className:"flex items-center gap-2",children:[l.jsx(xt,{size:16,className:"text-purple-500"}),"智能共识判断算法"]}),l.jsxs("div",{className:"flex items-center gap-2",children:[l.jsx(Xe,{size:16,className:"text-blue-500"}),"实时讨论模拟"]})]})]}),l.jsxs("div",{className:"space-y-8 mb-16",children:[l.jsxs("div",{className:"flex flex-wrap justify-center gap-6",children:[l.jsx("div",{className:"group",children:l.jsxs("div",{className:"bg-white rounded-2xl shadow-lg hover:shadow-xl transition-all p-8 border border-gray-100 group-hover:border-blue-200 fixed-size-card",children:[l.jsxs("div",{className:"flex items-center gap-4 mb-6",children:[l.jsx("div",{className:"w-16 h-16 bg-blue-100 rounded-xl flex items-center justify-center group-hover:bg-blue-200 transition-colors",children:l.jsx(xn,{size:32,className:"text-blue-600"})}),l.jsxs("div",{children:[l.jsx("h3",{className:"text-xl font-bold text-gray-900",children:"智能体管理"}),l.jsx("p",{className:"text-gray-500",children:"配置AI智能体"})]})]}),l.jsx("p",{className:"text-gray-600 mb-6 leading-relaxed",children:"创建和配置具有不同专业背景、思维方式和性格特征的智能体。 每个智能体都有独特的知识领域和讨论风格。"}),l.jsxs("div",{className:"mb-6",children:[l.jsxs("div",{className:"flex items-center justify-between text-sm text-gray-500 mb-2",children:[l.jsx("span",{children:"当前智能体数量"}),l.jsxs("span",{className:"font-bold text-blue-600 text-lg",children:[t.agents.length,"/8"]})]}),l.jsx("div",{className:"w-full h-2 bg-gray-200 rounded-full",children:l.jsx("div",{className:"h-full bg-blue-500 rounded-full transition-all",style:{width:`${t.agents.length/8*100}%`}})})]}),l.jsx("button",{onClick:()=>e("agents"),className:"w-full bg-blue-600 text-white py-3 rounded-xl hover:bg-blue-700 transition-colors font-medium",children:"管理智能体"})]})}),l.jsx("div",{className:"group",children:l.jsxs("div",{className:"bg-white rounded-2xl shadow-lg hover:shadow-xl transition-all p-8 border border-gray-100 group-hover:border-orange-200 fixed-size-card",children:[l.jsxs("div",{className:"flex items-center gap-4 mb-6",children:[l.jsx("div",{className:"w-16 h-16 bg-orange-100 rounded-xl flex items-center justify-center group-hover:bg-orange-200 transition-colors",children:l.jsx($c,{size:32,className:"text-orange-600"})}),l.jsxs("div",{children:[l.jsx("h3",{className:"text-xl font-bold text-gray-900",children:"LLM管理"}),l.jsx("p",{className:"text-gray-500",children:"配置大语言模型"})]})]}),l.jsx("p",{className:"text-gray-600 mb-6 leading-relaxed",children:"配置和管理大语言模型，为智能体提供真实的AI对话能力。 支持OpenAI、Anthropic等多种提供商。"}),l.jsxs("div",{className:"space-y-3 mb-6",children:[l.jsxs("div",{className:"flex items-center gap-2 text-sm text-gray-600",children:[l.jsx("div",{className:"w-2 h-2 bg-green-500 rounded-full"}),"支持多种LLM提供商"]}),l.jsxs("div",{className:"flex items-center gap-2 text-sm text-gray-600",children:[l.jsx("div",{className:"w-2 h-2 bg-blue-500 rounded-full"}),"个性化配置参数"]}),l.jsxs("div",{className:"flex items-center gap-2 text-sm text-gray-600",children:[l.jsx("div",{className:"w-2 h-2 bg-purple-500 rounded-full"}),"连接测试功能"]})]}),l.jsx("button",{onClick:()=>e("llm"),className:"w-full bg-orange-600 text-white py-3 rounded-xl hover:bg-orange-700 transition-colors font-medium",children:"管理LLM配置"})]})}),l.jsx("div",{className:"group",children:l.jsxs("div",{className:"bg-white rounded-2xl shadow-lg hover:shadow-xl transition-all p-8 border border-gray-100 group-hover:border-indigo-200 fixed-size-card",children:[l.jsxs("div",{className:"flex items-center gap-4 mb-6",children:[l.jsx("div",{className:"w-16 h-16 bg-indigo-100 rounded-xl flex items-center justify-center group-hover:bg-indigo-200 transition-colors",children:l.jsx(lo,{size:32,className:"text-indigo-600"})}),l.jsxs("div",{children:[l.jsx("h3",{className:"text-xl font-bold text-gray-900",children:"数据管理"}),l.jsx("p",{className:"text-gray-500",children:"备份与恢复"})]})]}),l.jsx("p",{className:"text-gray-600 mb-6 leading-relaxed",children:"管理系统数据的备份、恢复和清理，确保配置和历史记录的安全。"}),l.jsxs("div",{className:"space-y-3 mb-6",children:[l.jsxs("div",{className:"flex items-center gap-2 text-sm text-gray-600",children:[l.jsx("div",{className:"w-2 h-2 bg-green-500 rounded-full"}),"数据导出备份"]}),l.jsxs("div",{className:"flex items-center gap-2 text-sm text-gray-600",children:[l.jsx("div",{className:"w-2 h-2 bg-blue-500 rounded-full"}),"配置导入恢复"]}),l.jsxs("div",{className:"flex items-center gap-2 text-sm text-gray-600",children:[l.jsx("div",{className:"w-2 h-2 bg-red-500 rounded-full"}),"数据清理重置"]})]}),l.jsx("button",{onClick:()=>e("data"),className:"w-full bg-indigo-600 text-white py-3 rounded-xl hover:bg-indigo-700 transition-colors font-medium",children:"管理数据"})]})})]}),l.jsxs("div",{className:"flex flex-wrap justify-center gap-6",children:[l.jsx("div",{className:"group",children:l.jsxs("div",{className:"bg-white rounded-2xl shadow-lg hover:shadow-xl transition-all p-8 border border-gray-100 group-hover:border-purple-200 fixed-size-card",children:[l.jsxs("div",{className:"flex items-center gap-4 mb-6",children:[l.jsx("div",{className:"w-16 h-16 bg-purple-100 rounded-xl flex items-center justify-center group-hover:bg-purple-200 transition-colors",children:l.jsx(jn,{size:32,className:"text-purple-600"})}),l.jsxs("div",{children:[l.jsx("h3",{className:"text-xl font-bold text-gray-900",children:"创建讨论"}),l.jsx("p",{className:"text-gray-500",children:"配置讨论参数"})]})]}),l.jsx("p",{className:"text-gray-600 mb-6 leading-relaxed",children:"设置讨论话题、选择参与的智能体、配置讨论模式， 开始一场富有见解的AI讨论。"}),l.jsxs("div",{className:"space-y-3 mb-6",children:[l.jsxs("div",{className:"flex items-center gap-2 text-sm text-gray-600",children:[l.jsx("div",{className:"w-2 h-2 bg-green-500 rounded-full"}),"自由讨论模式"]}),l.jsxs("div",{className:"flex items-center gap-2 text-sm text-gray-600",children:[l.jsx("div",{className:"w-2 h-2 bg-blue-500 rounded-full"}),"主持人模式"]}),l.jsxs("div",{className:"flex items-center gap-2 text-sm text-gray-600",children:[l.jsx("div",{className:"w-2 h-2 bg-orange-500 rounded-full"}),"智能共识判断"]})]}),l.jsx("button",{onClick:()=>e("setup"),disabled:t.agents.length<2,className:"w-full bg-purple-600 text-white py-3 rounded-xl hover:bg-purple-700 disabled:bg-gray-400 disabled:cursor-not-allowed transition-colors font-medium",children:t.agents.length<2?"需要至少2个智能体":"创建新讨论"})]})}),l.jsx("div",{className:"group",children:l.jsxs("div",{className:"bg-white rounded-2xl shadow-lg hover:shadow-xl transition-all p-8 border border-gray-100 group-hover:border-green-200 fixed-size-card",children:[l.jsxs("div",{className:"flex items-center gap-4 mb-6",children:[l.jsx("div",{className:"w-16 h-16 bg-green-100 rounded-xl flex items-center justify-center group-hover:bg-green-200 transition-colors",children:l.jsx(Xe,{size:32,className:"text-green-600"})}),l.jsxs("div",{children:[l.jsx("h3",{className:"text-xl font-bold text-gray-900",children:"讨论状态"}),l.jsx("p",{className:"text-gray-500",children:"实时监控"})]})]}),t.isDiscussionActive?l.jsxs(l.Fragment,{children:[l.jsx("p",{className:"text-gray-600 mb-6 leading-relaxed",children:"当前有一场讨论正在进行中，您可以实时观看智能体之间的对话， 监控共识度变化。"}),l.jsxs("div",{className:"bg-green-50 border border-green-200 rounded-lg p-4 mb-6",children:[l.jsxs("div",{className:"flex items-center gap-2 mb-2",children:[l.jsx("div",{className:"w-2 h-2 bg-green-500 rounded-full animate-pulse"}),l.jsx("span",{className:"font-medium text-green-800",children:"讨论进行中"})]}),l.jsxs("p",{className:"text-green-700 text-sm",children:["话题：",(n=t.currentDiscussion)==null?void 0:n.topic]})]}),l.jsx("button",{onClick:()=>e("discussion"),className:"w-full bg-green-600 text-white py-3 rounded-xl hover:bg-green-700 transition-colors font-medium",children:"进入讨论室"})]}):l.jsxs(l.Fragment,{children:[l.jsx("p",{className:"text-gray-600 mb-6 leading-relaxed",children:"目前没有进行中的讨论。创建智能体并配置讨论参数后， 您就可以开始一场精彩的AI对话。"}),l.jsxs("div",{className:"bg-gray-50 border border-gray-200 rounded-lg p-4 mb-6",children:[l.jsxs("div",{className:"flex items-center gap-2 mb-2",children:[l.jsx("div",{className:"w-2 h-2 bg-gray-400 rounded-full"}),l.jsx("span",{className:"font-medium text-gray-600",children:"空闲状态"})]}),l.jsxs("p",{className:"text-gray-500 text-sm",children:["历史讨论：",t.allDiscussions.length," 场"]})]}),l.jsx("button",{onClick:()=>e(t.agents.length<2?"agents":"setup"),className:"w-full bg-gray-400 text-white py-3 rounded-xl hover:bg-gray-500 transition-colors font-medium",children:t.agents.length<2?"先创建智能体":"开始新讨论"})]})]})}),l.jsx("div",{className:"group",children:l.jsxs("div",{className:"bg-white rounded-2xl shadow-lg hover:shadow-xl transition-all p-8 border border-gray-100 group-hover:border-purple-200 fixed-size-card",children:[l.jsxs("div",{className:"flex items-center gap-4 mb-6",children:[l.jsx("div",{className:"w-16 h-16 bg-purple-100 rounded-xl flex items-center justify-center group-hover:bg-purple-200 transition-colors",children:l.jsx(gs,{size:32,className:"text-purple-600"})}),l.jsxs("div",{children:[l.jsx("h3",{className:"text-xl font-bold text-gray-900",children:"讨论历史"}),l.jsx("p",{className:"text-gray-500",children:"查看历史记录"})]})]}),l.jsx("p",{className:"text-gray-600 mb-6 leading-relaxed",children:"查看和分析历史讨论记录，了解智能体的对话模式和共识形成过程。"}),l.jsxs("div",{className:"mb-6",children:[l.jsxs("div",{className:"flex items-center justify-between text-sm text-gray-500 mb-2",children:[l.jsx("span",{children:"历史讨论数量"}),l.jsx("span",{className:"font-bold text-purple-600 text-lg",children:t.allDiscussions.length})]}),l.jsx("div",{className:"w-full h-2 bg-gray-200 rounded-full",children:l.jsx("div",{className:"h-full bg-purple-500 rounded-full transition-all",style:{width:`${Math.min(t.allDiscussions.length/10*100,100)}%`}})})]}),l.jsx("button",{onClick:()=>e("history"),className:"w-full bg-purple-600 text-white py-3 rounded-xl hover:bg-purple-700 transition-colors font-medium",children:"查看历史"})]})})]})]}),l.jsxs("div",{className:"bg-white rounded-2xl shadow-xl p-12 border border-gray-100",children:[l.jsx("h2",{className:"text-3xl font-bold text-gray-900 text-center mb-8",children:"系统特色功能"}),l.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8",children:[l.jsxs("div",{className:"text-center",children:[l.jsx("div",{className:"w-16 h-16 bg-blue-100 rounded-xl flex items-center justify-center mx-auto mb-4",children:l.jsx(xt,{size:32,className:"text-blue-600"})}),l.jsx("h3",{className:"font-semibold text-gray-900 mb-2",children:"智能化对话"}),l.jsx("p",{className:"text-gray-600 text-sm",children:"基于专业领域和性格特征生成真实的对话内容"})]}),l.jsxs("div",{className:"text-center",children:[l.jsx("div",{className:"w-16 h-16 bg-green-100 rounded-xl flex items-center justify-center mx-auto mb-4",children:l.jsx(wa,{size:32,className:"text-green-600"})}),l.jsx("h3",{className:"font-semibold text-gray-900 mb-2",children:"实时共识监控"}),l.jsx("p",{className:"text-gray-600 text-sm",children:"动态计算讨论共识度，智能判断达成一致的时机"})]}),l.jsxs("div",{className:"text-center",children:[l.jsx("div",{className:"w-16 h-16 bg-purple-100 rounded-xl flex items-center justify-center mx-auto mb-4",children:l.jsx(xn,{size:32,className:"text-purple-600"})}),l.jsx("h3",{className:"font-semibold text-gray-900 mb-2",children:"多模式讨论"}),l.jsx("p",{className:"text-gray-600 text-sm",children:"支持自由讨论和主持人模式，适应不同讨论需求"})]}),l.jsxs("div",{className:"text-center",children:[l.jsx("div",{className:"w-16 h-16 bg-orange-100 rounded-xl flex items-center justify-center mx-auto mb-4",children:l.jsx(Xe,{size:32,className:"text-orange-600"})}),l.jsx("h3",{className:"font-semibold text-gray-900 mb-2",children:"丰富的交互"}),l.jsx("p",{className:"text-gray-600 text-sm",children:"支持多种消息类型，包括陈述、提问、同意、反对"})]})]})]})]})})}function Nm(){return l.jsx(vm,{children:l.jsx(bp,{children:l.jsx(wm,{})})})}dl.createRoot(document.getElementById("root")).render(l.jsx(Pa.StrictMode,{children:l.jsx(Nm,{})}));
