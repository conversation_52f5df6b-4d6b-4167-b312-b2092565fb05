#!/usr/bin/env python3
"""
主持人功能测试脚本
创建测试数据并验证主持人功能
"""

import requests
import json
import time

BASE_URL = "http://localhost:5000/api"

def create_test_llm_config():
    """创建测试LLM配置"""
    config_data = {
        "name": "测试LLM配置",
        "provider": "openai",
        "model": "gpt-3.5-turbo",
        "apiKey": "test-api-key",
        "temperature": 0.7,
        "maxTokens": 1000,
        "systemPrompt": "你是一个智能助手"
    }
    
    response = requests.post(f"{BASE_URL}/llm-configs", json=config_data)
    if response.status_code == 201:
        print("✅ LLM配置创建成功")
        return response.json()
    else:
        print(f"❌ LLM配置创建失败: {response.text}")
        return None

def create_test_agents(llm_config_id):
    """创建测试智能体"""
    agents_data = [
        {
            "name": "主持人Alice",
            "avatar": "https://api.dicebear.com/7.x/avataaars/svg?seed=alice",
            "expertise": ["项目管理", "团队协作", "决策分析"],
            "thinkingStyle": "systematic",
            "personality": "diplomatic",
            "tools": ["会议管理", "冲突调解"],
            "isActive": True,
            "isModerator": True,
            "moderatorConfig": {
                "summaryFrequency": 3,
                "interventionThreshold": 0.6,
                "managementStyle": "flexible",
                "autoTerminate": True,
                "maxInterventions": 5
            },
            "llmConfig": {"id": llm_config_id}
        },
        {
            "name": "技术专家Bob",
            "avatar": "https://api.dicebear.com/7.x/avataaars/svg?seed=bob",
            "expertise": ["软件开发", "系统架构"],
            "thinkingStyle": "logical",
            "personality": "direct",
            "tools": ["代码分析", "技术评估"],
            "isActive": True,
            "isModerator": False,
            "llmConfig": {"id": llm_config_id}
        },
        {
            "name": "产品经理Carol",
            "avatar": "https://api.dicebear.com/7.x/avataaars/svg?seed=carol",
            "expertise": ["产品设计", "用户体验"],
            "thinkingStyle": "creative",
            "personality": "collaborative",
            "tools": ["用户调研", "原型设计"],
            "isActive": True,
            "isModerator": False,
            "llmConfig": {"id": llm_config_id}
        }
    ]
    
    created_agents = []
    for agent_data in agents_data:
        response = requests.post(f"{BASE_URL}/agents", json=agent_data)
        if response.status_code == 201:
            agent = response.json()
            created_agents.append(agent)
            print(f"✅ 智能体 {agent['name']} 创建成功")
        else:
            print(f"❌ 智能体创建失败: {response.text}")
    
    return created_agents

def create_test_discussion(agents):
    """创建测试讨论"""
    moderator = next((agent for agent in agents if agent.get('isModerator')), None)
    if not moderator:
        print("❌ 没有找到主持人")
        return None
    
    discussion_data = {
        "topic": "如何提升团队协作效率",
        "mode": "moderator",
        "participants": [agent['id'] for agent in agents],
        "moderatorId": moderator['id'],
        "status": "active"
    }
    
    response = requests.post(f"{BASE_URL}/discussions", json=discussion_data)
    if response.status_code == 201:
        discussion = response.json()
        print(f"✅ 讨论创建成功: {discussion['topic']}")
        print(f"   主持人: {moderator['name']}")
        print(f"   参与者: {len(agents)} 人")
        return discussion
    else:
        print(f"❌ 讨论创建失败: {response.text}")
        return None

def test_moderator_functionality():
    """测试主持人功能"""
    print("🚀 开始测试主持人功能...")
    
    # 1. 创建LLM配置
    llm_config = create_test_llm_config()
    if not llm_config:
        return
    
    # 2. 创建智能体
    agents = create_test_agents(llm_config['id'])
    if len(agents) < 3:
        print("❌ 智能体创建不足")
        return
    
    # 3. 创建讨论
    discussion = create_test_discussion(agents)
    if not discussion:
        return
    
    print("\n📊 测试结果:")
    print(f"   讨论ID: {discussion['id']}")
    print(f"   主持人ID: {discussion.get('moderatorId', '未设置')}")
    print(f"   话题相关性: {discussion.get('topicRelevanceScore', 1.0)}")
    print(f"   干预次数: {discussion.get('moderatorInterventions', 0)}")
    
    print("\n✅ 主持人功能测试数据创建完成！")
    print("   请在前端界面中查看主持人功能的实际效果。")

if __name__ == "__main__":
    test_moderator_functionality()
