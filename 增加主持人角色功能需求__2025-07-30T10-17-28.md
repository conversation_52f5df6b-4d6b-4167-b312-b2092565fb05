[ ] NAME:Current Task List DESCRIPTION:Root task for conversation __NEW_AGENT__
-[ ] NAME:主持人角色系统设计与实现 DESCRIPTION:设计和实现主持人角色的核心功能，包括数据模型、接口定义和基础逻辑
--[ ] NAME:数据模型扩展 DESCRIPTION:扩展Agent、Discussion和Message模型，支持主持人角色和相关功能
---[ ] NAME:扩展Agent模型 DESCRIPTION:为Agent模型添加isModerator字段和主持人相关配置
---[ ] NAME:扩展Discussion模型 DESCRIPTION:为Discussion模型添加moderatorId、moderatorSummaries等字段
---[ ] NAME:新增ModeratorAction模型 DESCRIPTION:创建主持人操作记录模型，记录总结、决策等操作
--[ ] NAME:主持人配置界面 DESCRIPTION:在讨论设置页面添加主持人选择和配置功能
---[ ] NAME:主持人选择组件 DESCRIPTION:在DiscussionSetup中添加主持人选择下拉框
---[ ] NAME:主持人配置选项 DESCRIPTION:添加主持人特殊配置：总结频率、干预程度等
---[ ] NAME:主持人预览功能 DESCRIPTION:显示选中主持人的信息和管理风格预览
--[ ] NAME:主持人核心逻辑实现 DESCRIPTION:实现主持人的核心功能：总结、共识度计算、话题相关性判断、发言人指定
---[ ] NAME:实时总结功能 DESCRIPTION:实现主持人对每轮对话的实时总结
---[ ] NAME:话题相关性判断 DESCRIPTION:实现主持人对讨论内容与主题相关性的判断
---[ ] NAME:智能发言人指定 DESCRIPTION:在主持人模式下，根据话题和参与者信息智能指定下一个发言人
---[ ] NAME:主持人引导发言 DESCRIPTION:实现主持人主动发言引导讨论的功能
---[ ] NAME:讨论终止决策 DESCRIPTION:实现主持人判断讨论是否应该终止的逻辑
--[ ] NAME:主持人显示界面 DESCRIPTION:在讨论室右侧添加主持人信息显示区域
---[ ] NAME:主持人信息面板 DESCRIPTION:在讨论室右侧添加主持人基本信息显示
---[ ] NAME:实时总结显示 DESCRIPTION:显示主持人的实时总结内容
---[ ] NAME:共识度分析显示 DESCRIPTION:显示主持人计算的详细共识度分析
---[ ] NAME:话题相关性指示器 DESCRIPTION:显示当前讨论与主题的相关性评分
---[ ] NAME:主持人决策日志 DESCRIPTION:显示主持人的决策过程和操作日志
--[ ] NAME:LLM集成与主持人智能 DESCRIPTION:集成真实LLM服务，实现主持人的智能分析和决策功能
---[ ] NAME:主持人专用LLM配置 DESCRIPTION:为主持人角色配置专用的LLM参数和提示词
---[ ] NAME:总结生成服务 DESCRIPTION:集成LLM实现智能总结生成
---[ ] NAME:相关性分析服务 DESCRIPTION:集成LLM实现话题相关性智能分析
---[ ] NAME:发言人选择服务 DESCRIPTION:集成LLM实现智能发言人选择
---[ ] NAME:主持人引导语言生成 DESCRIPTION:集成LLM生成主持人引导讨论的语言
--[ ] NAME:主持人历史记录系统 DESCRIPTION:实现主持人操作历史的记录、存储和分析功能
---[ ] NAME:主持人操作记录模型 DESCRIPTION:设计和实现主持人操作历史的数据模型
---[ ] NAME:操作记录API DESCRIPTION:实现主持人操作记录的增删改查API
---[ ] NAME:历史分析功能 DESCRIPTION:实现主持人操作效果的统计分析功能
---[ ] NAME:历史查看界面 DESCRIPTION:实现主持人历史记录的查看和分析界面
--[ ] NAME:测试与优化 DESCRIPTION:编写测试用例，验证主持人功能，并进行性能优化
---[ ] NAME:单元测试编写 DESCRIPTION:为主持人核心功能编写单元测试
---[ ] NAME:集成测试 DESCRIPTION:编写主持人功能的端到端集成测试
---[ ] NAME:性能优化 DESCRIPTION:优化主持人功能的响应速度和资源使用
---[ ] NAME:用户体验测试 DESCRIPTION:进行主持人功能的用户体验测试和优化