"""
数据库模型定义
对应前端的TypeScript接口
"""

from flask_sqlalchemy import SQLAlchemy
from datetime import datetime
import json
import uuid

db = SQLAlchemy()

class LLMConfig(db.Model):
    """LLM配置模型"""
    __tablename__ = 'llm_configs'
    
    id = db.Column(db.String(36), primary_key=True, default=lambda: str(uuid.uuid4()))
    name = db.Column(db.String(100), nullable=False)
    provider = db.Column(db.String(20), nullable=False)  # openai, anthropic, azure, custom
    model = db.Column(db.String(100), nullable=False)
    api_key = db.Column(db.Text, nullable=False)
    base_url = db.Column(db.String(255), nullable=True)
    temperature = db.Column(db.Float, default=0.7)
    max_tokens = db.Column(db.Integer, default=1000)
    system_prompt = db.Column(db.Text, nullable=True)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # 关联的智能体
    agents = db.relationship('Agent', backref='llm_config_ref', lazy=True)
    
    def to_dict(self):
        return {
            'id': self.id,
            'name': self.name,
            'provider': self.provider,
            'model': self.model,
            'apiKey': self.api_key,
            'baseURL': self.base_url,
            'temperature': self.temperature,
            'maxTokens': self.max_tokens,
            'systemPrompt': self.system_prompt
        }

class Agent(db.Model):
    """智能体模型"""
    __tablename__ = 'agents'

    id = db.Column(db.String(36), primary_key=True, default=lambda: str(uuid.uuid4()))
    name = db.Column(db.String(100), nullable=False)
    avatar = db.Column(db.String(255), nullable=False)
    expertise = db.Column(db.Text, nullable=False)  # JSON数组
    thinking_style = db.Column(db.String(50), nullable=False)
    personality = db.Column(db.String(50), nullable=False)
    tools = db.Column(db.Text, nullable=False)  # JSON数组
    is_active = db.Column(db.Boolean, default=True)
    llm_config_id = db.Column(db.String(36), db.ForeignKey('llm_configs.id'), nullable=False)

    # 主持人相关字段
    is_moderator = db.Column(db.Boolean, default=False)  # 是否可以作为主持人
    moderator_config = db.Column(db.Text, nullable=True)  # 主持人配置，JSON格式

    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # 关联的消息
    messages = db.relationship('Message', backref='agent_ref', lazy=True)
    
    def to_dict(self):
        return {
            'id': self.id,
            'name': self.name,
            'avatar': self.avatar,
            'expertise': json.loads(self.expertise) if self.expertise else [],
            'thinkingStyle': self.thinking_style,
            'personality': self.personality,
            'tools': json.loads(self.tools) if self.tools else [],
            'isActive': self.is_active,
            'isModerator': self.is_moderator,
            'moderatorConfig': json.loads(self.moderator_config) if self.moderator_config else None,
            'llmConfig': self.llm_config_ref.to_dict() if self.llm_config_ref else None
        }

class Discussion(db.Model):
    """讨论会话模型"""
    __tablename__ = 'discussions'

    id = db.Column(db.String(36), primary_key=True, default=lambda: str(uuid.uuid4()))
    topic = db.Column(db.String(255), nullable=False)
    mode = db.Column(db.String(20), nullable=False)  # moderator, free
    participants = db.Column(db.Text, nullable=False)  # JSON数组，存储智能体ID
    status = db.Column(db.String(20), default='active')  # active, consensus, ended
    consensus = db.Column(db.Text, nullable=True)
    consensus_score = db.Column(db.Float, default=0.0)

    # 主持人相关字段
    moderator_id = db.Column(db.String(36), db.ForeignKey('agents.id'), nullable=True)  # 主持人ID
    moderator_summaries = db.Column(db.Text, nullable=True)  # 主持人总结历史，JSON数组
    topic_relevance_score = db.Column(db.Float, default=1.0)  # 话题相关性评分
    moderator_interventions = db.Column(db.Integer, default=0)  # 主持人干预次数

    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # 关联的消息
    messages = db.relationship('Message', backref='discussion_ref', lazy=True, cascade='all, delete-orphan')

    # 关联的主持人
    moderator = db.relationship('Agent', foreign_keys=[moderator_id], backref='moderated_discussions')
    
    def to_dict(self):
        return {
            'id': self.id,
            'topic': self.topic,
            'mode': self.mode,
            'participants': json.loads(self.participants) if self.participants else [],
            'status': self.status,
            'consensus': self.consensus,
            'consensusScore': self.consensus_score,
            'moderatorId': self.moderator_id,
            'moderatorSummaries': json.loads(self.moderator_summaries) if self.moderator_summaries else [],
            'topicRelevanceScore': self.topic_relevance_score,
            'moderatorInterventions': self.moderator_interventions,
            'createdAt': self.created_at.isoformat() if self.created_at else None,
            'messages': [message.to_dict() for message in self.messages]
        }

class Message(db.Model):
    """消息模型"""
    __tablename__ = 'messages'
    
    id = db.Column(db.String(36), primary_key=True, default=lambda: str(uuid.uuid4()))
    agent_id = db.Column(db.String(36), db.ForeignKey('agents.id'), nullable=False)
    discussion_id = db.Column(db.String(36), db.ForeignKey('discussions.id'), nullable=False)
    content = db.Column(db.Text, nullable=False)
    message_type = db.Column(db.String(20), nullable=False)  # statement, question, agreement, disagreement
    reply_to = db.Column(db.String(36), nullable=True)
    timestamp = db.Column(db.DateTime, default=datetime.utcnow)
    
    def to_dict(self):
        return {
            'id': self.id,
            'agentId': self.agent_id,
            'content': self.content,
            'type': self.message_type,
            'replyTo': self.reply_to,
            'timestamp': self.timestamp.isoformat() if self.timestamp else None
        }

class AppSettings(db.Model):
    """应用设置模型"""
    __tablename__ = 'app_settings'
    
    id = db.Column(db.Integer, primary_key=True)
    version = db.Column(db.String(20), default='1.0.0')
    last_updated = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    auto_save = db.Column(db.Boolean, default=True)
    max_stored_discussions = db.Column(db.Integer, default=100)
    default_discussion_mode = db.Column(db.String(20), default='free')
    theme = db.Column(db.String(20), default='light')
    
    def to_dict(self):
        return {
            'version': self.version,
            'lastUpdated': self.last_updated.isoformat() if self.last_updated else None,
            'autoSave': self.auto_save,
            'maxStoredDiscussions': self.max_stored_discussions,
            'defaultDiscussionMode': self.default_discussion_mode,
            'theme': self.theme
        }

class ModeratorAction(db.Model):
    """主持人操作记录模型"""
    __tablename__ = 'moderator_actions'

    id = db.Column(db.String(36), primary_key=True, default=lambda: str(uuid.uuid4()))
    discussion_id = db.Column(db.String(36), db.ForeignKey('discussions.id'), nullable=False)
    moderator_id = db.Column(db.String(36), db.ForeignKey('agents.id'), nullable=False)
    action_type = db.Column(db.String(50), nullable=False)  # summary, intervention, speaker_selection, termination_decision
    action_content = db.Column(db.Text, nullable=False)  # 操作的具体内容
    context_data = db.Column(db.Text, nullable=True)  # 上下文数据，JSON格式
    relevance_score = db.Column(db.Float, nullable=True)  # 相关性评分（如果适用）
    consensus_score_before = db.Column(db.Float, nullable=True)  # 操作前的共识度
    consensus_score_after = db.Column(db.Float, nullable=True)  # 操作后的共识度
    timestamp = db.Column(db.DateTime, default=datetime.utcnow)

    # 关联关系
    discussion = db.relationship('Discussion', backref='moderator_actions')
    moderator = db.relationship('Agent', backref='moderator_actions')

    def to_dict(self):
        return {
            'id': self.id,
            'discussionId': self.discussion_id,
            'moderatorId': self.moderator_id,
            'actionType': self.action_type,
            'actionContent': self.action_content,
            'contextData': json.loads(self.context_data) if self.context_data else None,
            'relevanceScore': self.relevance_score,
            'consensusScoreBefore': self.consensus_score_before,
            'consensusScoreAfter': self.consensus_score_after,
            'timestamp': self.timestamp.isoformat() if self.timestamp else None
        }

class UserPreferences(db.Model):
    """用户偏好设置模型"""
    __tablename__ = 'user_preferences'
    
    id = db.Column(db.Integer, primary_key=True)
    default_agent_count = db.Column(db.Integer, default=3)
    preferred_llm_provider = db.Column(db.String(50), default='openai')
    auto_start_discussion = db.Column(db.Boolean, default=False)
    show_advanced_options = db.Column(db.Boolean, default=False)
    notifications_enabled = db.Column(db.Boolean, default=True)
    export_format = db.Column(db.String(20), default='json')
    
    def to_dict(self):
        return {
            'defaultAgentCount': self.default_agent_count,
            'preferredLLMProvider': self.preferred_llm_provider,
            'autoStartDiscussion': self.auto_start_discussion,
            'showAdvancedOptions': self.show_advanced_options,
            'notificationsEnabled': self.notifications_enabled,
            'exportFormat': self.export_format
        }
